<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysUserMapper">

    <resultMap type="SysUser" id="SysUserResult">
        <id     property="id"           column="id"             />
        <result property="openid"       column="openid"         />
        <result property="username"     column="username"       />
        <result property="nickname"     column="nickname"       />
        <result property="phone"        column="phone"          />
        <result property="avatar"       column="avatar"         />
        <result property="role"         column="role"           />
        <result property="status"       column="status"         />
        <result property="createdAt"    column="created_at"     />
        <result property="updatedAt"    column="updated_at"     />
    </resultMap>

    <sql id="selectUserVo">
        select id, openid, username, nickname, phone, avatar, role, status, created_at, updated_at
        from user
    </sql>

    <select id="selectUserByOpenid" parameterType="String" resultMap="SysUserResult">
        <include refid="selectUserVo"/>
        where openid = #{openid} and status = 1
    </select>

    <select id="selectUserByUsername" parameterType="String" resultMap="SysUserResult">
        <include refid="selectUserVo"/>
        where username = #{username} and status = 1
    </select>

    <select id="selectExecutorList" resultMap="SysUserResult">
        <include refid="selectUserVo"/>
        where role = 'EXECUTOR' and status = 1
        order by created_at desc
    </select>

    <select id="checkUsernameUnique" resultType="int">
        select count(1) from user where username = #{username}
        <if test="userId != null">
            and id != #{userId}
        </if>
        limit 1
    </select>

    <select id="checkOpenidUnique" resultType="int">
        select count(1) from user where openid = #{openid}
        <if test="userId != null">
            and id != #{userId}
        </if>
        limit 1
    </select>

</mapper>
