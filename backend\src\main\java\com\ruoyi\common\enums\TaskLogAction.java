package com.ruoyi.common.enums;

/**
 * 任务日志操作类型枚举
 * 
 * <AUTHOR>
 */
public enum TaskLogAction {
    
    CREATE("CREATE", "创建任务"),
    ASSIGN("ASSIGN", "派单"),
    ACCEPT("ACCEPT", "接单"),
    REJECT("REJECT", "拒单"),
    START("START", "开始执行"),
    COMPLETE("COMPLETE", "完成任务"),
    CANCEL("CANCEL", "取消任务");
    
    private final String code;
    private final String info;
    
    TaskLogAction(String code, String info) {
        this.code = code;
        this.info = info;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getInfo() {
        return info;
    }
}
