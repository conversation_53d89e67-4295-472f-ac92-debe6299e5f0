<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="f0bc44a4-a2ad-474c-8eea-df0a3f8d2772" name="Default Changelist" comment="" />
    <ignored path="$PROJECT_DIR$/target/" />
    <option name="EXCLUDED_CONVERTED_TO_IGNORED" value="true" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileEditorManager">
    <leaf>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/src/main/resources/application.yml">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="960">
              <caret line="48" column="22" selection-start-line="48" selection-start-column="22" selection-end-line="48" selection-end-column="22" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/src/main/java/com/ruoyi/TaskDispatchApplication.java">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="255">
              <caret line="17" column="5" selection-start-line="17" selection-start-column="5" selection-end-line="17" selection-end-column="5" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="true">
        <entry file="file://$PROJECT_DIR$/src/main/java/com/ruoyi/common/utils/file/FileUploadUtils.java">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="140">
              <caret line="27" column="7" lean-forward="true" selection-start-line="27" selection-start-column="7" selection-end-line="27" selection-end-column="7" />
              <folding>
                <element signature="imports" expanded="true" />
              </folding>
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/src/main/java/com/ruoyi/web/controller/wechat/WechatAuthController.java">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="108">
              <caret line="41" column="38" lean-forward="true" selection-start-line="41" selection-start-column="38" selection-end-line="41" selection-end-column="38" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/src/main/java/com/ruoyi/web/controller/common/CommonController.java">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="100">
              <caret line="5" column="34" selection-start-line="5" selection-start-column="34" selection-end-line="5" selection-end-column="34" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/pom.xml">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="190">
              <caret line="135" column="21" selection-start-line="135" selection-start-column="21" selection-end-line="135" selection-end-column="21" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/src/main/resources/mapper/system/TaskMapper.xml">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="180">
              <caret line="9" column="74" selection-start-line="9" selection-start-column="74" selection-end-line="9" selection-end-column="74" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/src/main/resources/mapper/system/TaskLogMapper.xml">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="1">
              <caret line="7" column="66" selection-start-line="7" selection-start-column="66" selection-end-line="7" selection-end-column="66" />
            </state>
          </provider>
        </entry>
      </file>
    </leaf>
  </component>
  <component name="FindInProjectRecents">
    <findStrings>
      <find>mysq</find>
      <find>spring-boot-starter-web</find>
    </findStrings>
  </component>
  <component name="IdeDocumentHistory">
    <option name="CHANGED_PATHS">
      <list>
        <option value="$PROJECT_DIR$/src/main/resources/application.yml" />
        <option value="$PROJECT_DIR$/pom.xml" />
        <option value="$PROJECT_DIR$/src/main/java/com/ruoyi/common/utils/file/FileUploadUtils.java" />
      </list>
    </option>
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="localRepository" value="D:\apache-maven-3.6.0\repository" />
        <option name="mavenHome" value="D:/apache-maven-3.6.0" />
        <option name="userSettingsFile" value="D:\apache-maven-3.6.0\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
    <option name="importingSettings">
      <MavenImportingSettings>
        <option name="importAutomatically" value="true" />
      </MavenImportingSettings>
    </option>
  </component>
  <component name="MavenProjectNavigator">
    <treeState>
      <expand>
        <path>
          <item name="" type="16c1761:MavenProjectsStructure$RootNode" />
          <item name="task-dispatch-system" type="9519ce18:MavenProjectsStructure$ProjectNode" />
        </path>
        <path>
          <item name="" type="16c1761:MavenProjectsStructure$RootNode" />
          <item name="task-dispatch-system" type="9519ce18:MavenProjectsStructure$ProjectNode" />
          <item name="Dependencies" type="f4be9f2a:MavenProjectsStructure$DependenciesNode" />
        </path>
      </expand>
      <select />
    </treeState>
  </component>
  <component name="ProjectFrameBounds" extendedState="7">
    <option name="x" value="88" />
    <option name="y" value="50" />
    <option name="width" value="1161" />
    <option name="height" value="698" />
  </component>
  <component name="ProjectView">
    <navigator proportions="" version="1">
      <foldersAlwaysOnTop value="true" />
    </navigator>
    <panes>
      <pane id="ProjectPane">
        <subPane>
          <expand>
            <path>
              <item name="task-dispatch-system" type="b2602c69:ProjectViewProjectNode" />
              <item name="backend" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="task-dispatch-system" type="b2602c69:ProjectViewProjectNode" />
              <item name="backend" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="task-dispatch-system" type="b2602c69:ProjectViewProjectNode" />
              <item name="backend" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
              <item name="main" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="task-dispatch-system" type="b2602c69:ProjectViewProjectNode" />
              <item name="backend" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
              <item name="main" type="462c0819:PsiDirectoryNode" />
              <item name="java" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="task-dispatch-system" type="b2602c69:ProjectViewProjectNode" />
              <item name="backend" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
              <item name="main" type="462c0819:PsiDirectoryNode" />
              <item name="java" type="462c0819:PsiDirectoryNode" />
              <item name="ruoyi" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="task-dispatch-system" type="b2602c69:ProjectViewProjectNode" />
              <item name="backend" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
              <item name="main" type="462c0819:PsiDirectoryNode" />
              <item name="java" type="462c0819:PsiDirectoryNode" />
              <item name="ruoyi" type="462c0819:PsiDirectoryNode" />
              <item name="common" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="task-dispatch-system" type="b2602c69:ProjectViewProjectNode" />
              <item name="backend" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
              <item name="main" type="462c0819:PsiDirectoryNode" />
              <item name="java" type="462c0819:PsiDirectoryNode" />
              <item name="ruoyi" type="462c0819:PsiDirectoryNode" />
              <item name="common" type="462c0819:PsiDirectoryNode" />
              <item name="file" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="task-dispatch-system" type="b2602c69:ProjectViewProjectNode" />
              <item name="backend" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
              <item name="main" type="462c0819:PsiDirectoryNode" />
              <item name="java" type="462c0819:PsiDirectoryNode" />
              <item name="ruoyi" type="462c0819:PsiDirectoryNode" />
              <item name="controller" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="task-dispatch-system" type="b2602c69:ProjectViewProjectNode" />
              <item name="backend" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
              <item name="main" type="462c0819:PsiDirectoryNode" />
              <item name="java" type="462c0819:PsiDirectoryNode" />
              <item name="ruoyi" type="462c0819:PsiDirectoryNode" />
              <item name="controller" type="462c0819:PsiDirectoryNode" />
              <item name="common" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="task-dispatch-system" type="b2602c69:ProjectViewProjectNode" />
              <item name="backend" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
              <item name="main" type="462c0819:PsiDirectoryNode" />
              <item name="java" type="462c0819:PsiDirectoryNode" />
              <item name="ruoyi" type="462c0819:PsiDirectoryNode" />
              <item name="controller" type="462c0819:PsiDirectoryNode" />
              <item name="wechat" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="task-dispatch-system" type="b2602c69:ProjectViewProjectNode" />
              <item name="backend" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
              <item name="main" type="462c0819:PsiDirectoryNode" />
              <item name="resources" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="task-dispatch-system" type="b2602c69:ProjectViewProjectNode" />
              <item name="backend" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
              <item name="main" type="462c0819:PsiDirectoryNode" />
              <item name="resources" type="462c0819:PsiDirectoryNode" />
              <item name="system" type="462c0819:PsiDirectoryNode" />
            </path>
          </expand>
          <select />
        </subPane>
      </pane>
      <pane id="PackagesPane" />
      <pane id="Scope" />
    </panes>
  </component>
  <component name="PropertiesComponent">
    <property name="WebServerToolWindowFactoryState" value="false" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$" />
    <property name="nodejs_interpreter_path.stuck_in_default_project" value="undefined stuck path" />
    <property name="nodejs_npm_path_reset_for_default_project" value="true" />
    <property name="settings.editor.selected.configurable" value="vcs.Git" />
  </component>
  <component name="RunDashboard">
    <option name="ruleStates">
      <list>
        <RuleState>
          <option name="name" value="ConfigurationTypeDashboardGroupingRule" />
        </RuleState>
        <RuleState>
          <option name="name" value="StatusDashboardGroupingRule" />
        </RuleState>
      </list>
    </option>
  </component>
  <component name="RunManager">
    <configuration name="TaskDispatchApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <module name="task-dispatch-system" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ruoyi.TaskDispatchApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SvnConfiguration">
    <configuration>C:\Users\<USER>\AppData\Roaming\Subversion</configuration>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="f0bc44a4-a2ad-474c-8eea-df0a3f8d2772" name="Default Changelist" comment="" />
      <created>1754872999988</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1754872999988</updated>
      <workItem from="1754873011964" duration="1821000" />
    </task>
    <servers />
  </component>
  <component name="TimeTrackingManager">
    <option name="totallyTimeSpent" value="1821000" />
  </component>
  <component name="ToolWindowManager">
    <frame x="-8" y="-8" width="1382" height="744" extended-state="6" />
    <editor active="true" />
    <layout>
      <window_info id="Web" side_tool="true" />
      <window_info id="Designer" />
      <window_info id="UI Designer" />
      <window_info id="Favorites" side_tool="true" />
      <window_info active="true" content_ui="combo" id="Project" order="0" visible="true" weight="0.18910742" />
      <window_info id="Structure" order="1" side_tool="true" weight="0.25" />
      <window_info anchor="bottom" id="Docker" show_stripe_button="false" />
      <window_info anchor="bottom" id="Database Changes" />
      <window_info anchor="bottom" id="Version Control" />
      <window_info anchor="bottom" id="Java Enterprise" />
      <window_info anchor="bottom" id="Spring" />
      <window_info anchor="bottom" id="Terminal" />
      <window_info anchor="bottom" id="Event Log" side_tool="true" />
      <window_info anchor="bottom" id="Messages" visible="true" weight="0.3289689" />
      <window_info anchor="bottom" id="Message" order="0" />
      <window_info anchor="bottom" id="Find" order="1" />
      <window_info anchor="bottom" id="Run" order="2" />
      <window_info anchor="bottom" id="Debug" order="3" weight="0.4" />
      <window_info anchor="bottom" id="Cvs" order="4" weight="0.25" />
      <window_info anchor="bottom" id="Inspection" order="5" weight="0.4" />
      <window_info anchor="bottom" id="TODO" order="6" />
      <window_info anchor="right" id="Palette" />
      <window_info anchor="right" id="Maven" weight="0.22541603" />
      <window_info anchor="right" id="Bean Validation" />
      <window_info anchor="right" id="Palette&#9;" />
      <window_info anchor="right" id="Database" />
      <window_info anchor="right" id="Commander" internal_type="SLIDING" order="0" type="SLIDING" weight="0.4" />
      <window_info anchor="right" id="Ant Build" order="1" weight="0.25" />
      <window_info anchor="right" content_ui="combo" id="Hierarchy" order="2" weight="0.25" />
    </layout>
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="1" />
  </component>
  <component name="editorHistoryManager">
    <entry file="file://$PROJECT_DIR$/src/main/resources/mapper/system/TaskMapper.xml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="180">
          <caret line="9" column="74" selection-start-line="9" selection-start-column="74" selection-end-line="9" selection-end-column="74" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/resources/mapper/system/SysUserMapper.xml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="-419">
          <caret line="14" column="66" selection-start-line="14" selection-start-column="66" selection-end-line="14" selection-end-column="66" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/resources/mapper/system/TaskLogMapper.xml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="1">
          <caret line="7" column="66" selection-start-line="7" selection-start-column="66" selection-end-line="7" selection-end-column="66" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/com/ruoyi/TaskDispatchApplication.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="255">
          <caret line="17" column="5" selection-start-line="17" selection-start-column="5" selection-end-line="17" selection-end-column="5" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/com/ruoyi/web/controller/common/CommonController.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="100">
          <caret line="5" column="34" selection-start-line="5" selection-start-column="34" selection-end-line="5" selection-end-column="34" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/resources/application.yml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="960">
          <caret line="48" column="22" selection-start-line="48" selection-start-column="22" selection-end-line="48" selection-end-column="22" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/pom.xml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="190">
          <caret line="135" column="21" selection-start-line="135" selection-start-column="21" selection-end-line="135" selection-end-column="21" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/com/ruoyi/web/controller/wechat/WechatAuthController.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="108">
          <caret line="41" column="38" lean-forward="true" selection-start-line="41" selection-start-column="38" selection-end-line="41" selection-end-column="38" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/com/ruoyi/common/utils/file/FileUploadUtils.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="140">
          <caret line="27" column="7" lean-forward="true" selection-start-line="27" selection-start-column="7" selection-end-line="27" selection-end-column="7" />
          <folding>
            <element signature="imports" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
  </component>
  <component name="masterDetails">
    <states>
      <state key="ProjectJDKs.UI">
        <settings>
          <last-edited>1.8</last-edited>
          <splitter-proportions>
            <option name="proportions">
              <list>
                <option value="0.2" />
              </list>
            </option>
          </splitter-proportions>
        </settings>
      </state>
    </states>
  </component>
</project>