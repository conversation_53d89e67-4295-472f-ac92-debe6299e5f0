<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.TaskLogMapper">

    <resultMap type="TaskLog" id="TaskLogResult">
        <id     property="id"           column="id"             />
        <result property="taskId"       column="task_id"        />
        <result property="action"       column="action"         />
        <result property="operatorId"   column="operator_id"    />
        <result property="operatorName" column="operator_name"  />
        <result property="remark"       column="remark"         />
        <result property="extraData"    column="extra_data"     />
        <result property="ts"           column="ts"             />
    </resultMap>

    <sql id="selectTaskLogVo">
        select id, task_id, action, operator_id, operator_name, remark, extra_data, ts
        from task_log
    </sql>

    <select id="selectLogsByTaskId" parameterType="Long" resultMap="TaskLogResult">
        <include refid="selectTaskLogVo"/>
        where task_id = #{taskId}
        order by ts desc
    </select>

    <select id="selectLogsByOperatorId" parameterType="Long" resultMap="TaskLogResult">
        <include refid="selectTaskLogVo"/>
        where operator_id = #{operatorId}
        order by ts desc
    </select>

    <insert id="insertBatch" parameterType="list">
        insert into task_log (task_id, action, operator_id, operator_name, remark, extra_data, ts)
        values
        <foreach collection="logs" item="log" separator=",">
            (#{log.taskId}, #{log.action}, #{log.operatorId}, #{log.operatorName}, 
             #{log.remark}, #{log.extraData}, #{log.ts})
        </foreach>
    </insert>

</mapper>
