import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { login, getUserInfo, logout } from '@/api/auth'

export const useUserStore = defineStore('user', () => {
  const token = ref(localStorage.getItem('token') || '')
  const userInfo = ref(null)

  const isLoggedIn = computed(() => !!token.value)
  const isAdmin = computed(() => userInfo.value?.role === 'ADMIN')

  // 初始化用户信息
  const initUser = async () => {
    if (token.value) {
      try {
        const res = await getUserInfo()
        userInfo.value = res.data
      } catch (error) {
        console.error('获取用户信息失败:', error)
        clearUser()
      }
    }
  }

  // 登录
  const loginUser = async (loginData) => {
    try {
      const res = await login(loginData)
      token.value = res.data.token
      userInfo.value = res.data.user
      localStorage.setItem('token', token.value)
      return res
    } catch (error) {
      throw error
    }
  }

  // 退出登录
  const logoutUser = async () => {
    try {
      await logout()
    } catch (error) {
      console.error('退出登录失败:', error)
    } finally {
      clearUser()
    }
  }

  // 清除用户信息
  const clearUser = () => {
    token.value = ''
    userInfo.value = null
    localStorage.removeItem('token')
  }

  // 更新用户信息
  const updateUserInfo = (newUserInfo) => {
    userInfo.value = { ...userInfo.value, ...newUserInfo }
  }

  return {
    token,
    userInfo,
    isLoggedIn,
    isAdmin,
    initUser,
    loginUser,
    logoutUser,
    clearUser,
    updateUserInfo
  }
})
