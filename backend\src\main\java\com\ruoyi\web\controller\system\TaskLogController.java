package com.ruoyi.web.controller.system;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.domain.TaskLog;
import com.ruoyi.system.service.ITaskLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 任务日志
 * 
 * <AUTHOR>
 */
@Api(tags = "任务日志管理")
@RestController
@RequestMapping("/system/taskLog")
public class TaskLogController {
    
    @Autowired
    private ITaskLogService taskLogService;

    /**
     * 查询任务日志列表
     */
    @ApiOperation("查询任务日志列表")
    @GetMapping("/list")
    public AjaxResult list(TaskLog taskLog) {
        List<TaskLog> list = taskLogService.selectTaskLogList(taskLog);
        return AjaxResult.success(list);
    }

    /**
     * 根据任务ID查询日志列表
     */
    @ApiOperation("根据任务ID查询日志列表")
    @GetMapping("/task/{taskId}")
    public AjaxResult getLogsByTaskId(@ApiParam("任务ID") @PathVariable Long taskId) {
        List<TaskLog> list = taskLogService.selectLogsByTaskId(taskId);
        return AjaxResult.success(list);
    }

    /**
     * 根据操作人ID查询日志列表
     */
    @ApiOperation("根据操作人ID查询日志列表")
    @GetMapping("/operator/{operatorId}")
    public AjaxResult getLogsByOperatorId(@ApiParam("操作人ID") @PathVariable Long operatorId) {
        List<TaskLog> list = taskLogService.selectLogsByOperatorId(operatorId);
        return AjaxResult.success(list);
    }

    /**
     * 删除任务日志
     */
    @ApiOperation("删除任务日志")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@ApiParam("日志ID数组") @PathVariable Long[] ids) {
        return AjaxResult.success(taskLogService.deleteTaskLogByIds(ids));
    }
}
