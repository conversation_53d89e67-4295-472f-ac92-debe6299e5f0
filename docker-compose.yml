version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: task-dispatch-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: task_dispatch_system
      MYSQL_USER: task_user
      MYSQL_PASSWORD: task_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - task-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: task-dispatch-redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - task-network

  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: task-dispatch-backend
    restart: always
    ports:
      - "8080:8080"
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SPRING_DATASOURCE_URL: **********************************************************************************************************************************************************
      SPRING_DATASOURCE_USERNAME: task_user
      SPRING_DATASOURCE_PASSWORD: task_password
      SPRING_REDIS_HOST: redis
      SPRING_REDIS_PORT: 6379
    depends_on:
      - mysql
      - redis
    volumes:
      - backend_logs:/app/logs
      - upload_files:/app/upload
    networks:
      - task-network

  # H5前端
  h5-frontend:
    build:
      context: ./frontend/h5
      dockerfile: Dockerfile
    container_name: task-dispatch-h5
    restart: always
    ports:
      - "3000:80"
    networks:
      - task-network

  # PC管理端前端
  admin-frontend:
    build:
      context: ./frontend/admin
      dockerfile: Dockerfile
    container_name: task-dispatch-admin
    restart: always
    ports:
      - "3001:80"
    networks:
      - task-network

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: task-dispatch-nginx
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./nginx/ssl:/etc/nginx/ssl
      - upload_files:/var/www/upload
    depends_on:
      - backend
      - h5-frontend
      - admin-frontend
    networks:
      - task-network

volumes:
  mysql_data:
  redis_data:
  backend_logs:
  upload_files:

networks:
  task-network:
    driver: bridge
