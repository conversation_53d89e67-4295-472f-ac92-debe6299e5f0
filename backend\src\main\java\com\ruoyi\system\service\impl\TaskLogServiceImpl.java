package com.ruoyi.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.domain.TaskLog;
import com.ruoyi.system.mapper.TaskLogMapper;
import com.ruoyi.system.service.ITaskLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * 任务日志 业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class TaskLogServiceImpl implements ITaskLogService {
    
    @Autowired
    private TaskLogMapper taskLogMapper;

    /**
     * 查询任务日志列表
     * 
     * @param taskLog 任务日志
     * @return 任务日志
     */
    @Override
    public List<TaskLog> selectTaskLogList(TaskLog taskLog) {
        LambdaQueryWrapper<TaskLog> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(taskLog.getTaskId() != null, TaskLog::getTaskId, taskLog.getTaskId())
               .eq(taskLog.getAction() != null, TaskLog::getAction, taskLog.getAction())
               .eq(taskLog.getOperatorId() != null, TaskLog::getOperatorId, taskLog.getOperatorId())
               .orderByDesc(TaskLog::getTs);
        return taskLogMapper.selectList(wrapper);
    }

    /**
     * 根据任务ID查询日志列表
     * 
     * @param taskId 任务ID
     * @return 日志列表
     */
    @Override
    public List<TaskLog> selectLogsByTaskId(Long taskId) {
        return taskLogMapper.selectLogsByTaskId(taskId);
    }

    /**
     * 根据操作人ID查询日志列表
     * 
     * @param operatorId 操作人ID
     * @return 日志列表
     */
    @Override
    public List<TaskLog> selectLogsByOperatorId(Long operatorId) {
        return taskLogMapper.selectLogsByOperatorId(operatorId);
    }

    /**
     * 新增任务日志
     * 
     * @param taskLog 任务日志
     * @return 结果
     */
    @Override
    public int insertTaskLog(TaskLog taskLog) {
        return taskLogMapper.insert(taskLog);
    }

    /**
     * 批量新增任务日志
     * 
     * @param logs 日志列表
     * @return 结果
     */
    @Override
    public int insertBatch(List<TaskLog> logs) {
        return taskLogMapper.insertBatch(logs);
    }

    /**
     * 删除任务日志
     * 
     * @param id 任务日志主键
     * @return 结果
     */
    @Override
    public int deleteTaskLogById(Long id) {
        return taskLogMapper.deleteById(id);
    }

    /**
     * 批量删除任务日志
     * 
     * @param ids 需要删除的任务日志主键集合
     * @return 结果
     */
    @Override
    public int deleteTaskLogByIds(Long[] ids) {
        return taskLogMapper.deleteBatchIds(Arrays.asList(ids));
    }
}
