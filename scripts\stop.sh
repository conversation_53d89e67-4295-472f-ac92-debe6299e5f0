#!/bin/bash

# 派单接单系统停止脚本
# 作者: ruoyi
# 版本: 1.0.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 停止服务
stop_services() {
    log_info "停止派单接单系统服务..."
    
    if [ -f "docker-compose.yml" ]; then
        docker-compose down
        log_info "服务已停止"
    else
        log_error "docker-compose.yml 文件不存在"
        exit 1
    fi
}

# 清理容器
cleanup_containers() {
    log_info "清理容器..."
    
    containers=("task-dispatch-mysql" "task-dispatch-redis" "task-dispatch-backend" "task-dispatch-h5" "task-dispatch-admin" "task-dispatch-nginx")
    
    for container in "${containers[@]}"; do
        if docker ps -a | grep -q "$container"; then
            log_debug "删除容器: $container"
            docker rm -f "$container" 2>/dev/null || true
        fi
    done
    
    log_info "容器清理完成"
}

# 清理镜像（可选）
cleanup_images() {
    if [ "$1" = "--remove-images" ]; then
        log_info "清理镜像..."
        
        images=("task-dispatch-backend" "task-dispatch-h5" "task-dispatch-admin")
        
        for image in "${images[@]}"; do
            if docker images | grep -q "$image"; then
                log_debug "删除镜像: $image"
                docker rmi "$image" 2>/dev/null || true
            fi
        done
        
        # 清理悬空镜像
        docker image prune -f
        
        log_info "镜像清理完成"
    fi
}

# 清理数据卷（可选）
cleanup_volumes() {
    if [ "$1" = "--remove-data" ]; then
        log_warn "清理数据卷（这将删除所有数据）..."
        read -p "确定要删除所有数据吗？(y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            docker volume prune -f
            log_info "数据卷清理完成"
        else
            log_info "跳过数据卷清理"
        fi
    fi
}

# 显示帮助信息
show_help() {
    echo "派单接单系统停止脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --remove-images    同时删除构建的镜像"
    echo "  --remove-data      同时删除数据卷（危险操作）"
    echo "  --help            显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                 仅停止服务"
    echo "  $0 --remove-images 停止服务并删除镜像"
    echo "  $0 --remove-data   停止服务并删除所有数据"
}

# 主函数
main() {
    case "$1" in
        --help)
            show_help
            exit 0
            ;;
        --remove-images)
            stop_services
            cleanup_containers
            cleanup_images "--remove-images"
            ;;
        --remove-data)
            stop_services
            cleanup_containers
            cleanup_volumes "--remove-data"
            ;;
        "")
            stop_services
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
    
    log_info "停止完成！"
}

# 执行主函数
main "$@"
