{"name": "task-dispatch-admin", "version": "1.0.0", "description": "派单接单系统PC管理端", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"vue": "^3.3.4", "vue-router": "^4.2.4", "pinia": "^2.1.6", "axios": "^1.4.0", "element-plus": "^2.3.8", "@element-plus/icons-vue": "^2.1.0", "echarts": "^5.4.3", "dayjs": "^1.11.9", "nprogress": "^0.2.0", "js-cookie": "^3.0.5"}, "devDependencies": {"@vitejs/plugin-vue": "^4.2.3", "vite": "^4.4.5", "sass": "^1.64.1", "unplugin-auto-import": "^0.16.6", "unplugin-vue-components": "^0.25.1"}, "keywords": ["vue3", "vite", "element-plus", "admin", "task-dispatch"], "author": "ruoyi", "license": "MIT"}