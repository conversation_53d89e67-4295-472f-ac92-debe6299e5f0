package com.ruoyi.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.enums.UserRole;
import com.ruoyi.system.domain.SysUser;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;

/**
 * 用户 业务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysUserServiceImpl implements ISysUserService {

    @Autowired
    private SysUserMapper userMapper;

    /**
     * 根据条件分页查询用户列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    public List<SysUser> selectUserList(SysUser user) {
        LambdaQueryWrapper<SysUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StringUtils.hasText(user.getUsername()), SysUser::getUsername, user.getUsername())
               .like(StringUtils.hasText(user.getNickname()), SysUser::getNickname, user.getNickname())
               .like(StringUtils.hasText(user.getPhone()), SysUser::getPhone, user.getPhone())
               .eq(user.getRole() != null, SysUser::getRole, user.getRole())
               .eq(user.getStatus() != null, SysUser::getStatus, user.getStatus())
               .orderByDesc(SysUser::getCreatedAt);
        return userMapper.selectList(wrapper);
    }

    /**
     * 根据用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserById(Long userId) {
        return userMapper.selectById(userId);
    }

    /**
     * 根据用户名查询用户
     *
     * @param username 用户名
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserByUsername(String username) {
        return userMapper.selectUserByUsername(username);
    }

    /**
     * 根据openid查询用户
     *
     * @param openid 微信openid
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserByOpenid(String openid) {
        return userMapper.selectUserByOpenid(openid);
    }

    /**
     * 校验用户名称是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public String checkUsernameUnique(SysUser user) {
        Long userId = user.getId() == null ? -1L : user.getId();
        SysUser info = userMapper.selectUserByUsername(user.getUsername());
        if (info != null && info.getId().longValue() != userId.longValue()) {
            return "1";
        }
        return "0";
    }

    /**
     * 校验openid是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public String checkOpenidUnique(SysUser user) {
        Long userId = user.getId() == null ? -1L : user.getId();
        int count = userMapper.checkOpenidUnique(user.getOpenid(), userId);
        return count > 0 ? "1" : "0";
    }

    /**
     * 新增用户
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int insertUser(SysUser user) {
        return userMapper.insert(user);
    }

    /**
     * 修改用户
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int updateUser(SysUser user) {
        return userMapper.updateById(user);
    }

    /**
     * 删除用户
     *
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    public int deleteUserById(Long userId) {
        return userMapper.deleteById(userId);
    }

    /**
     * 批量删除用户
     *
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
    @Override
    public int deleteUserByIds(Long[] userIds) {
        return userMapper.deleteBatchIds(Arrays.asList(userIds));
    }

    /**
     * 查询执行人员列表
     *
     * @return 执行人员列表
     */
    @Override
    public List<SysUser> selectExecutorList() {
        return userMapper.selectExecutorList();
    }

    /**
     * 微信登录或注册
     *
     * @param openid 微信openid
     * @param nickname 昵称
     * @param avatar 头像
     * @return 用户信息
     */
    @Override
    public SysUser wechatLoginOrRegister(String openid, String nickname, String avatar) {
        SysUser user = selectUserByOpenid(openid);
        if (user == null) {
            // 新用户注册
            user = new SysUser();
            user.setOpenid(openid);
            user.setUsername("wx_" + openid.substring(openid.length() - 8));
            user.setNickname(nickname);
            user.setAvatar(avatar);
            user.setRole(UserRole.EXECUTOR);
            user.setStatus(1);
            insertUser(user);
        } else {
            // 更新用户信息
            if (StringUtils.hasText(nickname)) {
                user.setNickname(nickname);
            }
            if (StringUtils.hasText(avatar)) {
                user.setAvatar(avatar);
            }
            updateUser(user);
        }
        return user;
    }
}
