import request from '@/utils/request'

// 微信登录
export function login(data) {
  return request({
    url: '/wechat/auth/login',
    method: 'post',
    data
  })
}

// 获取用户信息
export function getUserInfo() {
  return request({
    url: '/wechat/auth/userinfo',
    method: 'get'
  })
}

// 退出登录
export function logout() {
  return request({
    url: '/wechat/auth/logout',
    method: 'post'
  })
}

// 获取微信授权URL
export function getAuthUrl(redirectUri) {
  return request({
    url: '/wechat/auth/getAuthUrl',
    method: 'get',
    params: { redirectUri }
  })
}

// 微信授权回调
export function authCallback(code) {
  return request({
    url: '/wechat/auth/callback',
    method: 'get',
    params: { code }
  })
}
