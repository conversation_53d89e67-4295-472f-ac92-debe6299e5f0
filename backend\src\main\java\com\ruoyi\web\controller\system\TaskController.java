package com.ruoyi.web.controller.system;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.domain.Task;
import com.ruoyi.system.service.ITaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 任务管理
 * 
 * <AUTHOR>
 */
@Api(tags = "任务管理")
@RestController
@RequestMapping("/system/task")
public class TaskController {
    
    @Autowired
    private ITaskService taskService;

    /**
     * 查询任务列表
     */
    @ApiOperation("查询任务列表")
    @GetMapping("/list")
    public AjaxResult list(Task task) {
        List<Task> list = taskService.selectTaskList(task);
        return AjaxResult.success(list);
    }

    /**
     * 获取任务详细信息
     */
    @ApiOperation("获取任务详情")
    @GetMapping(value = "/{taskId}")
    public AjaxResult getInfo(@ApiParam("任务ID") @PathVariable("taskId") Long taskId) {
        return AjaxResult.success(taskService.selectTaskById(taskId));
    }

    /**
     * 新增任务
     */
    @ApiOperation("新增任务")
    @PostMapping
    public AjaxResult add(@Validated @RequestBody Task task) {
        return AjaxResult.success(taskService.insertTask(task));
    }

    /**
     * 修改任务
     */
    @ApiOperation("修改任务")
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody Task task) {
        return AjaxResult.success(taskService.updateTask(task));
    }

    /**
     * 删除任务
     */
    @ApiOperation("删除任务")
    @DeleteMapping("/{taskIds}")
    public AjaxResult remove(@ApiParam("任务ID数组") @PathVariable Long[] taskIds) {
        return AjaxResult.success(taskService.deleteTaskByIds(taskIds));
    }

    /**
     * 派单
     */
    @ApiOperation("派单")
    @PostMapping("/{taskId}/assign")
    public AjaxResult assign(
            @ApiParam("任务ID") @PathVariable Long taskId,
            @ApiParam("执行人ID") @RequestParam Long executorId,
            @ApiParam("操作人ID") @RequestParam Long operatorId) {
        boolean result = taskService.assignTask(taskId, executorId, operatorId);
        return result ? AjaxResult.success("派单成功") : AjaxResult.error("派单失败");
    }

    /**
     * 接单
     */
    @ApiOperation("接单")
    @PostMapping("/{taskId}/accept")
    public AjaxResult accept(
            @ApiParam("任务ID") @PathVariable Long taskId,
            @ApiParam("用户ID") @RequestParam Long userId) {
        boolean result = taskService.acceptTask(taskId, userId);
        return result ? AjaxResult.success("接单成功") : AjaxResult.error("接单失败");
    }

    /**
     * 拒单
     */
    @ApiOperation("拒单")
    @PostMapping("/{taskId}/reject")
    public AjaxResult reject(
            @ApiParam("任务ID") @PathVariable Long taskId,
            @ApiParam("用户ID") @RequestParam Long userId,
            @ApiParam("拒绝原因") @RequestParam(required = false) String reason) {
        boolean result = taskService.rejectTask(taskId, userId, reason);
        return result ? AjaxResult.success("拒单成功") : AjaxResult.error("拒单失败");
    }

    /**
     * 开始执行任务
     */
    @ApiOperation("开始执行任务")
    @PostMapping("/{taskId}/start")
    public AjaxResult start(
            @ApiParam("任务ID") @PathVariable Long taskId,
            @ApiParam("用户ID") @RequestParam Long userId) {
        boolean result = taskService.startTask(taskId, userId);
        return result ? AjaxResult.success("开始执行成功") : AjaxResult.error("开始执行失败");
    }

    /**
     * 完成任务
     */
    @ApiOperation("完成任务")
    @PostMapping("/{taskId}/complete")
    public AjaxResult complete(
            @ApiParam("任务ID") @PathVariable Long taskId,
            @ApiParam("用户ID") @RequestParam Long userId,
            @ApiParam("执行结果") @RequestParam String result,
            @ApiParam("图片URLs") @RequestParam(required = false) String images) {
        boolean success = taskService.completeTask(taskId, userId, result, images);
        return success ? AjaxResult.success("任务完成") : AjaxResult.error("完成任务失败");
    }

    /**
     * 取消任务
     */
    @ApiOperation("取消任务")
    @PostMapping("/{taskId}/cancel")
    public AjaxResult cancel(
            @ApiParam("任务ID") @PathVariable Long taskId,
            @ApiParam("用户ID") @RequestParam Long userId,
            @ApiParam("取消原因") @RequestParam(required = false) String reason) {
        boolean result = taskService.cancelTask(taskId, userId, reason);
        return result ? AjaxResult.success("取消成功") : AjaxResult.error("取消失败");
    }

    /**
     * 查询用户的任务列表
     */
    @ApiOperation("查询用户的任务列表")
    @GetMapping("/user/{userId}")
    public AjaxResult getUserTasks(
            @ApiParam("用户ID") @PathVariable Long userId,
            @ApiParam("任务状态") @RequestParam(required = false) String status) {
        List<Task> list = taskService.selectTasksByUserId(userId, status);
        return AjaxResult.success(list);
    }

    /**
     * 查询任务统计数据
     */
    @ApiOperation("查询任务统计数据")
    @GetMapping("/statistics")
    public AjaxResult getStatistics() {
        List<Map<String, Object>> statistics = taskService.selectTaskStatistics();
        return AjaxResult.success(statistics);
    }

    /**
     * 查询用户任务统计数据
     */
    @ApiOperation("查询用户任务统计数据")
    @GetMapping("/statistics/user/{userId}")
    public AjaxResult getUserStatistics(@ApiParam("用户ID") @PathVariable Long userId) {
        Map<String, Object> statistics = taskService.selectUserTaskStatistics(userId);
        return AjaxResult.success(statistics);
    }

    /**
     * 查询即将到期的任务
     */
    @ApiOperation("查询即将到期的任务")
    @GetMapping("/deadline")
    public AjaxResult getTasksNearDeadline(@ApiParam("小时数") @RequestParam(defaultValue = "24") int hours) {
        List<Task> list = taskService.selectTasksNearDeadline(hours);
        return AjaxResult.success(list);
    }
}
