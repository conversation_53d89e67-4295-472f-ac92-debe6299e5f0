<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.TaskMapper">

    <resultMap type="Task" id="TaskResult">
        <id     property="id"               column="id"                 />
        <result property="title"            column="title"              />
        <result property="description"      column="description"        />
        <result property="type"             column="type"               />
        <result property="status"           column="status"             />
        <result property="priority"         column="priority"           />
        <result property="creatorId"        column="creator_id"         />
        <result property="executorId"       column="executor_id"        />
        <result property="merchantName"     column="merchant_name"      />
        <result property="merchantAddress"  column="merchant_address"   />
        <result property="contactPerson"    column="contact_person"     />
        <result property="contactPhone"     column="contact_phone"      />
        <result property="deadline"         column="deadline"           />
        <result property="assignedAt"       column="assigned_at"        />
        <result property="startedAt"        column="started_at"         />
        <result property="completedAt"      column="completed_at"       />
        <result property="result"           column="result"             />
        <result property="images"           column="images"             />
        <result property="remark"           column="remark"             />
        <result property="createdAt"        column="created_at"         />
        <result property="updatedAt"        column="updated_at"         />
        <result property="creatorName"      column="creator_name"       />
        <result property="executorName"     column="executor_name"      />
    </resultMap>

    <sql id="selectTaskVo">
        select t.id, t.title, t.description, t.type, t.status, t.priority,
               t.creator_id, t.executor_id, t.merchant_name, t.merchant_address,
               t.contact_person, t.contact_phone, t.deadline, t.assigned_at,
               t.started_at, t.completed_at, t.result, t.images, t.remark,
               t.created_at, t.updated_at
        from task t
    </sql>

    <sql id="selectTaskWithNamesVo">
        select t.id, t.title, t.description, t.type, t.status, t.priority,
               t.creator_id, t.executor_id, t.merchant_name, t.merchant_address,
               t.contact_person, t.contact_phone, t.deadline, t.assigned_at,
               t.started_at, t.completed_at, t.result, t.images, t.remark,
               t.created_at, t.updated_at,
               c.nickname as creator_name,
               e.nickname as executor_name
        from task t
        left join user c on t.creator_id = c.id
        left join user e on t.executor_id = e.id
    </sql>

    <select id="selectTaskListWithNames" parameterType="Task" resultMap="TaskResult">
        <include refid="selectTaskWithNamesVo"/>
        <where>
            <if test="title != null and title != ''">
                and t.title like concat('%', #{title}, '%')
            </if>
            <if test="type != null">
                and t.type = #{type}
            </if>
            <if test="status != null">
                and t.status = #{status}
            </if>
            <if test="creatorId != null">
                and t.creator_id = #{creatorId}
            </if>
            <if test="executorId != null">
                and t.executor_id = #{executorId}
            </if>
            <if test="merchantName != null and merchantName != ''">
                and t.merchant_name like concat('%', #{merchantName}, '%')
            </if>
        </where>
        order by t.created_at desc
    </select>

    <select id="selectTaskByIdWithNames" parameterType="Long" resultMap="TaskResult">
        <include refid="selectTaskWithNamesVo"/>
        where t.id = #{id}
    </select>

    <select id="selectTasksByUserId" resultMap="TaskResult">
        <include refid="selectTaskWithNamesVo"/>
        where t.executor_id = #{userId}
        <if test="status != null and status != ''">
            and t.status = #{status}
        </if>
        order by t.created_at desc
    </select>

    <select id="selectTaskStatistics" resultType="map">
        select 
            DATE(created_at) as date,
            type,
            status,
            count(*) as count
        from task 
        where created_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        group by DATE(created_at), type, status
        order by date desc
    </select>

    <select id="selectUserTaskStatistics" parameterType="Long" resultType="map">
        select 
            count(*) as total_tasks,
            count(case when status = 'COMPLETED' then 1 end) as completed_tasks,
            count(case when status = 'IN_PROGRESS' then 1 end) as in_progress_tasks,
            count(case when status = 'ASSIGNED' then 1 end) as assigned_tasks
        from task 
        where executor_id = #{userId}
    </select>

    <select id="selectTasksNearDeadline" parameterType="int" resultMap="TaskResult">
        <include refid="selectTaskWithNamesVo"/>
        where t.deadline is not null 
        and t.deadline <= DATE_ADD(NOW(), INTERVAL #{hours} HOUR)
        and t.status in ('ASSIGNED', 'IN_PROGRESS')
        order by t.deadline asc
    </select>

</mapper>
