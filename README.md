# 派单接单系统

基于Spring Boot + 若依框架 + Vue3 的任务派单接单系统，支持H5移动端和PC管理端。

## 系统特性

- 🚀 **现代化技术栈**: Spring Boot 2.7 + Vue 3 + Element Plus + Vant
- 📱 **多端支持**: H5移动端 + PC管理端
- 🔐 **微信集成**: 支持微信授权登录
- 📊 **数据统计**: 任务统计、用户统计等
- 🐳 **容器化部署**: Docker + Docker Compose 一键部署
- 🔒 **权限控制**: 基于角色的权限管理
- 📝 **任务流程**: 创建 → 派单 → 接单 → 执行 → 完成

## 技术架构

### 后端技术栈
- Spring Boot 2.7.18
- 若依框架
- MyBatis-Plus 3.5.3
- MySQL 8.0
- Redis 7.0
- JWT认证
- 微信SDK

### 前端技术栈
- Vue 3.3
- Vite 4.4
- Element Plus (PC端)
- Vant 4.6 (H5端)
- Pinia状态管理
- Axios网络请求

## 功能模块

### H5移动端
- 微信授权登录
- 任务列表查看
- 任务详情查看
- 接单/拒单操作
- 任务执行记录
- 图片上传
- 个人中心

### PC管理端
- 用户管理
- 任务管理
- 派单操作
- 数据统计
- 系统设置

## 快速开始

### 环境要求
- Docker 20.0+
- Docker Compose 2.0+
- 8GB+ 内存
- 20GB+ 磁盘空间

### 一键部署

1. 克隆项目
```bash
git clone <repository-url>
cd task_mch
```

2. 启动系统
```bash
chmod +x scripts/start.sh
./scripts/start.sh
```

3. 访问系统
- H5端: http://localhost/h5/
- PC管理端: http://localhost/admin/
- API文档: http://localhost/api/doc.html

### 默认账号
- 管理员: admin / 123456
- 执行人员: executor1 / 123456

## 手动部署

### 1. 数据库初始化
```bash
# 创建数据库
mysql -u root -p < database/init.sql
```

### 2. 后端部署
```bash
cd backend
mvn clean package -DskipTests
java -jar target/task-dispatch-system-1.0.0.jar
```

### 3. 前端部署
```bash
# H5端
cd frontend/h5
npm install
npm run build

# PC管理端
cd frontend/admin
npm install
npm run build
```

### 4. Nginx配置
```bash
# 复制nginx配置
cp nginx/conf.d/default.conf /etc/nginx/conf.d/
nginx -s reload
```

## 配置说明

### 微信配置
修改 `backend/src/main/resources/application.yml`:
```yaml
wechat:
  mp:
    app-id: your_app_id
    secret: your_app_secret
    token: your_token
    aes-key: your_aes_key
```

### 数据库配置
```yaml
spring:
  datasource:
    url: ************************************************
    username: your_username
    password: your_password
```

### Redis配置
```yaml
spring:
  redis:
    host: localhost
    port: 6379
    password: your_password
```

## API文档

系统集成了Knife4j API文档，启动后访问：
http://localhost:8080/api/doc.html

## 目录结构

```
task_mch/
├── backend/                 # 后端代码
│   ├── src/
│   ├── pom.xml
│   └── Dockerfile
├── frontend/
│   ├── h5/                 # H5移动端
│   └── admin/              # PC管理端
├── database/               # 数据库脚本
├── nginx/                  # Nginx配置
├── scripts/                # 部署脚本
├── docker-compose.yml      # Docker编排
└── README.md
```

## 开发指南

### 后端开发
1. 导入IDE（推荐IntelliJ IDEA）
2. 安装MySQL、Redis
3. 修改配置文件
4. 运行TaskDispatchApplication

### 前端开发
```bash
# H5端开发
cd frontend/h5
npm install
npm run dev

# PC端开发
cd frontend/admin
npm install
npm run dev
```

## 部署运维

### 查看日志
```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f backend
```

### 停止服务
```bash
./scripts/stop.sh
```

### 备份数据
```bash
# 备份数据库
docker exec task-dispatch-mysql mysqldump -u root -p123456 task_dispatch_system > backup.sql

# 备份上传文件
docker cp task-dispatch-backend:/app/upload ./upload_backup
```

## 常见问题

### 1. 端口冲突
如果默认端口被占用，修改docker-compose.yml中的端口映射。

### 2. 内存不足
建议至少8GB内存，可以调整JVM参数：
```yaml
environment:
  JAVA_OPTS: "-Xms256m -Xmx512m"
```

### 3. 微信授权失败
检查微信公众号配置和回调域名设置。

## 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系我们

- 项目地址: https://github.com/your-org/task-dispatch-system
- 问题反馈: https://github.com/your-org/task-dispatch-system/issues
- 邮箱: <EMAIL>

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 基础任务管理功能
- 微信授权登录
- H5和PC双端支持
- Docker容器化部署
