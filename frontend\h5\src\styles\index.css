/* 全局样式 */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, 'PingFang SC', 'mi<PERSON>', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
  background-color: #f7f8fa;
}

/* 覆盖vant默认样式 */
.van-nav-bar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.van-nav-bar .van-nav-bar__title,
.van-nav-bar .van-nav-bar__text {
  color: white;
}

.van-nav-bar .van-icon {
  color: white;
}

/* 卡片样式 */
.van-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 按钮样式 */
.van-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

.van-button--success {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  border: none;
}

/* 表单样式 */
.van-field__control {
  font-size: 16px;
}

/* 标签样式 */
.van-tag {
  border-radius: 12px;
}

/* 浮动按钮样式 */
.van-floating-bubble {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

/* 工具类 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.mt-4 {
  margin-top: 4px;
}

.mt-8 {
  margin-top: 8px;
}

.mt-12 {
  margin-top: 12px;
}

.mt-16 {
  margin-top: 16px;
}

.mb-4 {
  margin-bottom: 4px;
}

.mb-8 {
  margin-bottom: 8px;
}

.mb-12 {
  margin-bottom: 12px;
}

.mb-16 {
  margin-bottom: 16px;
}

.ml-4 {
  margin-left: 4px;
}

.ml-8 {
  margin-left: 8px;
}

.mr-4 {
  margin-right: 4px;
}

.mr-8 {
  margin-right: 8px;
}

.p-4 {
  padding: 4px;
}

.p-8 {
  padding: 8px;
}

.p-12 {
  padding: 12px;
}

.p-16 {
  padding: 16px;
}

.px-4 {
  padding-left: 4px;
  padding-right: 4px;
}

.px-8 {
  padding-left: 8px;
  padding-right: 8px;
}

.px-12 {
  padding-left: 12px;
  padding-right: 12px;
}

.px-16 {
  padding-left: 16px;
  padding-right: 16px;
}

.py-4 {
  padding-top: 4px;
  padding-bottom: 4px;
}

.py-8 {
  padding-top: 8px;
  padding-bottom: 8px;
}

.py-12 {
  padding-top: 12px;
  padding-bottom: 12px;
}

.py-16 {
  padding-top: 16px;
  padding-bottom: 16px;
}

/* 文字颜色 */
.text-primary {
  color: #1989fa;
}

.text-success {
  color: #07c160;
}

.text-warning {
  color: #ff976a;
}

.text-danger {
  color: #ee0a24;
}

.text-gray {
  color: #969799;
}

.text-gray-light {
  color: #c8c9cc;
}

/* 字体大小 */
.text-xs {
  font-size: 12px;
}

.text-sm {
  font-size: 14px;
}

.text-base {
  font-size: 16px;
}

.text-lg {
  font-size: 18px;
}

.text-xl {
  font-size: 20px;
}

.text-2xl {
  font-size: 24px;
}

/* 字体粗细 */
.font-normal {
  font-weight: normal;
}

.font-medium {
  font-weight: 500;
}

.font-bold {
  font-weight: bold;
}
