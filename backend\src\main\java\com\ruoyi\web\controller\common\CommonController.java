package com.ruoyi.web.controller.common;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.common.utils.file.FileUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 通用请求处理
 * 
 * <AUTHOR>
 */
@Api(tags = "通用接口")
@RestController
@RequestMapping("/common")
public class CommonController {
    
    private static final Logger log = LoggerFactory.getLogger(CommonController.class);

    @Value("${ruoyi.profile}")
    private String profile;

    private static final String FILE_DELIMETER = ",";

    /**
     * 通用上传请求（单个）
     */
    @ApiOperation("单文件上传")
    @PostMapping("/upload")
    public AjaxResult uploadFile(MultipartFile file) throws Exception {
        try {
            // 上传文件路径
            String filePath = FileUploadUtils.upload(profile, file);
            String url = "/profile" + filePath;
            AjaxResult ajax = AjaxResult.success();
            ajax.put("url", url);
            ajax.put("fileName", file.getOriginalFilename());
            ajax.put("newFileName", FileUtils.getName(filePath));
            ajax.put("originalFilename", file.getOriginalFilename());
            return ajax;
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 通用上传请求（多个）
     */
    @ApiOperation("多文件上传")
    @PostMapping("/uploads")
    public AjaxResult uploadFiles(List<MultipartFile> files) throws Exception {
        try {
            // 上传文件路径
            List<String> urls = new ArrayList<String>();
            List<String> fileNames = new ArrayList<String>();
            List<String> newFileNames = new ArrayList<String>();
            List<String> originalFilenames = new ArrayList<String>();
            for (MultipartFile file : files) {
                // 上传文件路径
                String filePath = FileUploadUtils.upload(profile, file);
                String url = "/profile" + filePath;
                urls.add(url);
                fileNames.add(file.getOriginalFilename());
                newFileNames.add(FileUtils.getName(filePath));
                originalFilenames.add(file.getOriginalFilename());
            }
            AjaxResult ajax = AjaxResult.success();
            ajax.put("urls", String.join(FILE_DELIMETER, urls));
            ajax.put("fileNames", String.join(FILE_DELIMETER, fileNames));
            ajax.put("newFileNames", String.join(FILE_DELIMETER, newFileNames));
            ajax.put("originalFilenames", String.join(FILE_DELIMETER, originalFilenames));
            return ajax;
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 本地资源通用下载
     */
    @ApiOperation("文件下载")
    @GetMapping("/download/resource")
    public void resourceDownload(String resource, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        try {
            if (!FileUtils.checkAllowDownload(resource)) {
                throw new Exception("资源文件(" + resource + ")非法，不允许下载。 ");
            }
            // 本地资源路径
            String localPath = profile;
            // 数据库资源地址
            String downloadPath = localPath + resource;
            // 下载名称
            String downloadName = resource.substring(resource.lastIndexOf("/") + 1);
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, downloadName);
            FileUtils.writeBytes(downloadPath, response.getOutputStream());
        } catch (Exception e) {
            log.error("下载文件失败", e);
        }
    }
}
