package com.ruoyi.web.controller.wechat;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.domain.SysUser;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.framework.web.service.TokenService;
import com.ruoyi.common.core.domain.model.LoginUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.result.WxMpOAuth2AccessToken;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 微信授权登录
 * 
 * <AUTHOR>
 */
@Api(tags = "微信授权登录")
@RestController
@RequestMapping("/wechat/auth")
public class WechatAuthController {
    
    private static final Logger log = LoggerFactory.getLogger(WechatAuthController.class);
    
    @Autowired
    private WxMpService wxMpService;
    
    @Autowired
    private ISysUserService userService;
    
    @Autowired
    private TokenService tokenService;

    /**
     * 获取微信授权URL
     */
    @ApiOperation("获取微信授权URL")
    @GetMapping("/getAuthUrl")
    public AjaxResult getAuthUrl(@ApiParam("回调地址") @RequestParam String redirectUri) {
        try {
            String authUrl = wxMpService.getOAuth2Service().buildAuthorizationUrl(
                redirectUri, "snsapi_userinfo", null);
            return AjaxResult.success("获取授权URL成功", authUrl);
        } catch (Exception e) {
            log.error("获取微信授权URL失败", e);
            return AjaxResult.error("获取授权URL失败：" + e.getMessage());
        }
    }

    /**
     * 微信授权回调
     */
    @ApiOperation("微信授权回调")
    @GetMapping("/callback")
    public AjaxResult callback(@ApiParam("授权码") @RequestParam String code) {
        try {
            // 获取access_token
            WxMpOAuth2AccessToken accessToken = wxMpService.getOAuth2Service().getAccessToken(code);
            String openid = accessToken.getOpenId();
            
            // 获取用户信息
            WxMpUser wxUser = wxMpService.getOAuth2Service().getUserInfo(accessToken, null);
            
            // 登录或注册用户
            SysUser user = userService.wechatLoginOrRegister(openid, wxUser.getNickname(), wxUser.getHeadImgUrl());
            
            // 生成token
            LoginUser loginUser = new LoginUser(user.getId(), user.getUsername(), user);
            String token = tokenService.createToken(loginUser);
            
            Map<String, Object> result = new HashMap<>();
            result.put("token", token);
            result.put("user", user);
            
            return AjaxResult.success("登录成功", result);
        } catch (WxErrorException e) {
            log.error("微信授权失败", e);
            return AjaxResult.error("微信授权失败：" + e.getError().getErrorMsg());
        } catch (Exception e) {
            log.error("登录处理失败", e);
            return AjaxResult.error("登录处理失败：" + e.getMessage());
        }
    }

    /**
     * 微信登录（通过openid）
     */
    @ApiOperation("微信登录")
    @PostMapping("/login")
    public AjaxResult login(@RequestBody Map<String, String> params) {
        try {
            String openid = params.get("openid");
            String nickname = params.get("nickname");
            String avatar = params.get("avatar");
            
            if (openid == null || openid.trim().isEmpty()) {
                return AjaxResult.error("openid不能为空");
            }
            
            // 登录或注册用户
            SysUser user = userService.wechatLoginOrRegister(openid, nickname, avatar);
            
            // 生成token
            LoginUser loginUser = new LoginUser(user.getId(), user.getUsername(), user);
            String token = tokenService.createToken(loginUser);
            
            Map<String, Object> result = new HashMap<>();
            result.put("token", token);
            result.put("user", user);
            
            return AjaxResult.success("登录成功", result);
        } catch (Exception e) {
            log.error("微信登录失败", e);
            return AjaxResult.error("登录失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户信息
     */
    @ApiOperation("获取用户信息")
    @GetMapping("/userinfo")
    public AjaxResult getUserInfo(@RequestHeader("Authorization") String token) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(token);
            if (loginUser == null) {
                return AjaxResult.error("用户未登录");
            }
            
            SysUser user = userService.selectUserById(loginUser.getUserId());
            return AjaxResult.success("获取用户信息成功", user);
        } catch (Exception e) {
            log.error("获取用户信息失败", e);
            return AjaxResult.error("获取用户信息失败：" + e.getMessage());
        }
    }

    /**
     * 退出登录
     */
    @ApiOperation("退出登录")
    @PostMapping("/logout")
    public AjaxResult logout(@RequestHeader("Authorization") String token) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(token);
            if (loginUser != null) {
                tokenService.delLoginUser(token);
            }
            return AjaxResult.success("退出成功");
        } catch (Exception e) {
            log.error("退出登录失败", e);
            return AjaxResult.error("退出失败：" + e.getMessage());
        }
    }
}
