package com.ruoyi.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.system.domain.Task;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 任务Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface TaskMapper extends BaseMapper<Task> {
    
    /**
     * 查询任务列表（包含创建人和执行人姓名）
     * 
     * @param task 任务查询条件
     * @return 任务列表
     */
    List<Task> selectTaskListWithNames(Task task);
    
    /**
     * 根据ID查询任务详情（包含创建人和执行人姓名）
     * 
     * @param id 任务ID
     * @return 任务详情
     */
    Task selectTaskByIdWithNames(@Param("id") Long id);
    
    /**
     * 查询用户的任务列表
     * 
     * @param userId 用户ID
     * @param status 任务状态（可选）
     * @return 任务列表
     */
    List<Task> selectTasksByUserId(@Param("userId") Long userId, @Param("status") String status);
    
    /**
     * 查询任务统计数据
     * 
     * @return 统计数据
     */
    List<Map<String, Object>> selectTaskStatistics();
    
    /**
     * 查询用户任务统计数据
     * 
     * @param userId 用户ID
     * @return 统计数据
     */
    Map<String, Object> selectUserTaskStatistics(@Param("userId") Long userId);
    
    /**
     * 查询即将到期的任务
     * 
     * @param hours 小时数
     * @return 任务列表
     */
    List<Task> selectTasksNearDeadline(@Param("hours") int hours);
}
