package com.ruoyi.web.controller.system;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.domain.SysUser;
import com.ruoyi.system.service.ISysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户信息
 * 
 * <AUTHOR>
 */
@Api(tags = "用户管理")
@RestController
@RequestMapping("/system/user")
public class SysUserController {
    
    @Autowired
    private ISysUserService userService;

    /**
     * 获取用户列表
     */
    @ApiOperation("获取用户列表")
    @GetMapping("/list")
    public AjaxResult list(SysUser user) {
        List<SysUser> list = userService.selectUserList(user);
        return AjaxResult.success(list);
    }

    /**
     * 根据用户编号获取详细信息
     */
    @ApiOperation("获取用户详情")
    @GetMapping(value = "/{userId}")
    public AjaxResult getInfo(@ApiParam("用户ID") @PathVariable Long userId) {
        return AjaxResult.success(userService.selectUserById(userId));
    }

    /**
     * 新增用户
     */
    @ApiOperation("新增用户")
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysUser user) {
        if ("1".equals(userService.checkUsernameUnique(user))) {
            return AjaxResult.error("新增用户'" + user.getUsername() + "'失败，登录账号已存在");
        }
        if (user.getOpenid() != null && "1".equals(userService.checkOpenidUnique(user))) {
            return AjaxResult.error("新增用户'" + user.getUsername() + "'失败，微信openid已存在");
        }
        return AjaxResult.success(userService.insertUser(user));
    }

    /**
     * 修改用户
     */
    @ApiOperation("修改用户")
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysUser user) {
        if ("1".equals(userService.checkUsernameUnique(user))) {
            return AjaxResult.error("修改用户'" + user.getUsername() + "'失败，登录账号已存在");
        }
        if (user.getOpenid() != null && "1".equals(userService.checkOpenidUnique(user))) {
            return AjaxResult.error("修改用户'" + user.getUsername() + "'失败，微信openid已存在");
        }
        return AjaxResult.success(userService.updateUser(user));
    }

    /**
     * 删除用户
     */
    @ApiOperation("删除用户")
    @DeleteMapping("/{userIds}")
    public AjaxResult remove(@ApiParam("用户ID数组") @PathVariable Long[] userIds) {
        return AjaxResult.success(userService.deleteUserByIds(userIds));
    }

    /**
     * 获取执行人员列表
     */
    @ApiOperation("获取执行人员列表")
    @GetMapping("/executors")
    public AjaxResult getExecutors() {
        List<SysUser> list = userService.selectExecutorList();
        return AjaxResult.success(list);
    }

    /**
     * 状态修改
     */
    @ApiOperation("修改用户状态")
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody SysUser user) {
        return AjaxResult.success(userService.updateUser(user));
    }
}
