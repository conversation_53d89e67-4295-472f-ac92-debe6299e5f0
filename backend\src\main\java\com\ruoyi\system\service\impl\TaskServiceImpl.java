package com.ruoyi.system.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.enums.TaskLogAction;
import com.ruoyi.common.enums.TaskStatus;
import com.ruoyi.system.domain.Task;
import com.ruoyi.system.domain.TaskLog;
import com.ruoyi.system.domain.SysUser;
import com.ruoyi.system.mapper.TaskMapper;
import com.ruoyi.system.service.ITaskService;
import com.ruoyi.system.service.ITaskLogService;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 任务 业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class TaskServiceImpl implements ITaskService {
    
    @Autowired
    private TaskMapper taskMapper;
    
    @Autowired
    private ITaskLogService taskLogService;
    
    @Autowired
    private ISysUserService userService;

    /**
     * 查询任务列表
     * 
     * @param task 任务信息
     * @return 任务集合
     */
    @Override
    public List<Task> selectTaskList(Task task) {
        List<Task> tasks = taskMapper.selectTaskListWithNames(task);
        // 处理图片JSON字符串
        for (Task t : tasks) {
            if (StringUtils.hasText(t.getImages())) {
                try {
                    List<String> imageList = JSON.parseArray(t.getImages(), String.class);
                    t.setImageList(imageList);
                } catch (Exception e) {
                    // 忽略JSON解析错误
                }
            }
        }
        return tasks;
    }

    /**
     * 根据任务ID查询任务详情
     * 
     * @param taskId 任务ID
     * @return 任务对象
     */
    @Override
    public Task selectTaskById(Long taskId) {
        Task task = taskMapper.selectTaskByIdWithNames(taskId);
        if (task != null && StringUtils.hasText(task.getImages())) {
            try {
                List<String> imageList = JSON.parseArray(task.getImages(), String.class);
                task.setImageList(imageList);
            } catch (Exception e) {
                // 忽略JSON解析错误
            }
        }
        return task;
    }

    /**
     * 新增任务
     * 
     * @param task 任务信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertTask(Task task) {
        task.setStatus(TaskStatus.PENDING);
        int result = taskMapper.insert(task);
        
        // 记录日志
        if (result > 0) {
            SysUser creator = userService.selectUserById(task.getCreatorId());
            TaskLog log = new TaskLog(task.getId(), TaskLogAction.CREATE, 
                task.getCreatorId(), creator.getNickname(), "创建任务");
            taskLogService.insertTaskLog(log);
        }
        
        return result;
    }

    /**
     * 修改任务
     * 
     * @param task 任务信息
     * @return 结果
     */
    @Override
    public int updateTask(Task task) {
        return taskMapper.updateById(task);
    }

    /**
     * 删除任务
     * 
     * @param taskId 任务ID
     * @return 结果
     */
    @Override
    public int deleteTaskById(Long taskId) {
        return taskMapper.deleteById(taskId);
    }

    /**
     * 批量删除任务
     * 
     * @param taskIds 需要删除的任务ID
     * @return 结果
     */
    @Override
    public int deleteTaskByIds(Long[] taskIds) {
        return taskMapper.deleteBatchIds(Arrays.asList(taskIds));
    }

    /**
     * 派单
     * 
     * @param taskId 任务ID
     * @param executorId 执行人ID
     * @param operatorId 操作人ID
     * @return 结果
     */
    @Override
    @Transactional
    public boolean assignTask(Long taskId, Long executorId, Long operatorId) {
        Task task = taskMapper.selectById(taskId);
        if (task == null || task.getStatus() != TaskStatus.PENDING) {
            return false;
        }
        
        // 更新任务状态
        task.setExecutorId(executorId);
        task.setStatus(TaskStatus.ASSIGNED);
        task.setAssignedAt(LocalDateTime.now());
        int result = taskMapper.updateById(task);
        
        // 记录日志
        if (result > 0) {
            SysUser operator = userService.selectUserById(operatorId);
            SysUser executor = userService.selectUserById(executorId);
            TaskLog log = new TaskLog(taskId, TaskLogAction.ASSIGN, 
                operatorId, operator.getNickname(), 
                "派单给：" + executor.getNickname());
            taskLogService.insertTaskLog(log);
        }
        
        return result > 0;
    }

    /**
     * 接单
     * 
     * @param taskId 任务ID
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    @Transactional
    public boolean acceptTask(Long taskId, Long userId) {
        Task task = taskMapper.selectById(taskId);
        if (task == null || task.getStatus() != TaskStatus.ASSIGNED || 
            !task.getExecutorId().equals(userId)) {
            return false;
        }
        
        // 更新任务状态
        task.setStatus(TaskStatus.IN_PROGRESS);
        task.setStartedAt(LocalDateTime.now());
        int result = taskMapper.updateById(task);
        
        // 记录日志
        if (result > 0) {
            SysUser user = userService.selectUserById(userId);
            TaskLog log = new TaskLog(taskId, TaskLogAction.ACCEPT, 
                userId, user.getNickname(), "接受任务");
            taskLogService.insertTaskLog(log);
        }
        
        return result > 0;
    }

    /**
     * 拒单
     * 
     * @param taskId 任务ID
     * @param userId 用户ID
     * @param reason 拒绝原因
     * @return 结果
     */
    @Override
    @Transactional
    public boolean rejectTask(Long taskId, Long userId, String reason) {
        Task task = taskMapper.selectById(taskId);
        if (task == null || task.getStatus() != TaskStatus.ASSIGNED || 
            !task.getExecutorId().equals(userId)) {
            return false;
        }
        
        // 更新任务状态
        task.setExecutorId(null);
        task.setStatus(TaskStatus.PENDING);
        task.setAssignedAt(null);
        int result = taskMapper.updateById(task);
        
        // 记录日志
        if (result > 0) {
            SysUser user = userService.selectUserById(userId);
            TaskLog log = new TaskLog(taskId, TaskLogAction.REJECT, 
                userId, user.getNickname(), "拒绝任务：" + reason);
            taskLogService.insertTaskLog(log);
        }
        
        return result > 0;
    }

    /**
     * 开始执行任务
     * 
     * @param taskId 任务ID
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    @Transactional
    public boolean startTask(Long taskId, Long userId) {
        Task task = taskMapper.selectById(taskId);
        if (task == null || task.getStatus() != TaskStatus.ASSIGNED || 
            !task.getExecutorId().equals(userId)) {
            return false;
        }
        
        // 更新任务状态
        task.setStatus(TaskStatus.IN_PROGRESS);
        task.setStartedAt(LocalDateTime.now());
        int result = taskMapper.updateById(task);
        
        // 记录日志
        if (result > 0) {
            SysUser user = userService.selectUserById(userId);
            TaskLog log = new TaskLog(taskId, TaskLogAction.START, 
                userId, user.getNickname(), "开始执行任务");
            taskLogService.insertTaskLog(log);
        }
        
        return result > 0;
    }

    /**
     * 完成任务
     * 
     * @param taskId 任务ID
     * @param userId 用户ID
     * @param result 执行结果
     * @param images 图片URLs
     * @return 结果
     */
    @Override
    @Transactional
    public boolean completeTask(Long taskId, Long userId, String result, String images) {
        Task task = taskMapper.selectById(taskId);
        if (task == null || task.getStatus() != TaskStatus.IN_PROGRESS || 
            !task.getExecutorId().equals(userId)) {
            return false;
        }
        
        // 更新任务状态
        task.setStatus(TaskStatus.COMPLETED);
        task.setCompletedAt(LocalDateTime.now());
        task.setResult(result);
        task.setImages(images);
        int updateResult = taskMapper.updateById(task);
        
        // 记录日志
        if (updateResult > 0) {
            SysUser user = userService.selectUserById(userId);
            TaskLog log = new TaskLog(taskId, TaskLogAction.COMPLETE, 
                userId, user.getNickname(), "完成任务");
            taskLogService.insertTaskLog(log);
        }
        
        return updateResult > 0;
    }

    /**
     * 取消任务
     * 
     * @param taskId 任务ID
     * @param userId 用户ID
     * @param reason 取消原因
     * @return 结果
     */
    @Override
    @Transactional
    public boolean cancelTask(Long taskId, Long userId, String reason) {
        Task task = taskMapper.selectById(taskId);
        if (task == null || task.getStatus() == TaskStatus.COMPLETED || 
            task.getStatus() == TaskStatus.CANCELLED) {
            return false;
        }
        
        // 更新任务状态
        task.setStatus(TaskStatus.CANCELLED);
        int result = taskMapper.updateById(task);
        
        // 记录日志
        if (result > 0) {
            SysUser user = userService.selectUserById(userId);
            TaskLog log = new TaskLog(taskId, TaskLogAction.CANCEL, 
                userId, user.getNickname(), "取消任务：" + reason);
            taskLogService.insertTaskLog(log);
        }
        
        return result > 0;
    }

    /**
     * 查询用户的任务列表
     * 
     * @param userId 用户ID
     * @param status 任务状态（可选）
     * @return 任务列表
     */
    @Override
    public List<Task> selectTasksByUserId(Long userId, String status) {
        return taskMapper.selectTasksByUserId(userId, status);
    }

    /**
     * 查询任务统计数据
     * 
     * @return 统计数据
     */
    @Override
    public List<Map<String, Object>> selectTaskStatistics() {
        return taskMapper.selectTaskStatistics();
    }

    /**
     * 查询用户任务统计数据
     * 
     * @param userId 用户ID
     * @return 统计数据
     */
    @Override
    public Map<String, Object> selectUserTaskStatistics(Long userId) {
        return taskMapper.selectUserTaskStatistics(userId);
    }

    /**
     * 查询即将到期的任务
     * 
     * @param hours 小时数
     * @return 任务列表
     */
    @Override
    public List<Task> selectTasksNearDeadline(int hours) {
        return taskMapper.selectTasksNearDeadline(hours);
    }
}
