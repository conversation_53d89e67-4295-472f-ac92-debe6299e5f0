# 派单／接单系统设计文档

> 技术栈：Spring Boot、若依（RuoYi）框架、MyBatis-Plus、MySQL、Redis、Nginx

---

## 1. 概述

- **目标**：设计一个包含 H5 用户端（基于微信使用）和 PC 管理端的任务派单系统。任务形式包括商户维护（激活、巡检、设备维护、政策宣贯、达标回访等）。系统支持管理员在 PC 端和 H5 管理端创建任务并派单，支持接单与任务执行流程。
- **受众**：产品经理、后端/前端开发工程师、测试工程师、运维工程师。

---

## 2. 功能需求

### 2.1 H5 用户端（管理员/执行人员）
- 微信授权登录
- 查看任务列表与详情
- 接单、拒单
- 执行任务（填写结果、上传图片）
- 查看历史任务
- 管理员可在 H5 端创建任务并派单

### 2.2 PC 管理端（运营/管理员）
- 用户与执行人员管理
- 任务管理：创建、修改、删除
- 派单（手动选择执行人员）
- 查看任务执行状态与历史记录
- 简单数据统计（任务数量、完成率）

### 2.3 系统非功能需求
- 部署方式：传统前后端分离（前端 Nginx 部署，后端 Spring Boot 独立部署）
- 数据一致性：任务状态实时更新
- 安全性：接口权限控制

---

## 3. 技术选型与架构

### 3.1 技术栈
- 后端：Spring Boot + 若依框架 + MyBatis-Plus
- 数据库：MySQL
- 缓存：Redis
- 前端：H5（微信端）、PC（Vue）
- 认证：JWT + 若依权限系统
- 文件存储：本地或对象存储

### 3.2 系统架构
- 表示层（Controller）：RESTful API
- 业务层（Service）：任务处理逻辑
- 持久层（Mapper）：MyBatis-Plus
- 集成层：缓存、文件存储、微信 API

---

## 4. 数据库设计（概要）

### 4.1 `user`（用户表）
- id
- openid
- name
- role (ADMIN, EXECUTOR)
- status
- created_at, updated_at

### 4.2 `task`（任务表）
- id
- title
- description
- type（激活、巡检、设备维护、政策宣贯、达标回访）
- status (PENDING, ASSIGNED, IN_PROGRESS, COMPLETED, CANCELLED)
- creator_id
- executor_id
- created_at, updated_at

### 4.3 `task_log`（任务日志表）
- id
- task_id
- action（CREATE, ASSIGN, START, COMPLETE, CANCEL）
- operator_id
- remark
- ts

---

## 5. 核心流程

### 5.1 任务创建与派单
1. 管理员（PC 或 H5）创建任务
2. 任务状态为 PENDING
3. 管理员选择执行人员派单
4. 状态更新为 ASSIGNED

### 5.2 接单与执行
1. 执行人员接单 -> 状态变为 IN_PROGRESS
2. 执行人员完成任务 -> 上传结果 -> 状态变为 COMPLETED

---

## 6. 前后端主要接口与流程

### 6.1 后端主要接口（RESTful）
- **用户管理**
  - GET `/api/users`：获取用户列表
  - POST `/api/users`：创建用户
  - PUT `/api/users/{id}`：更新用户信息
  - DELETE `/api/users/{id}`：删除用户

- **任务管理**
  - GET `/api/tasks`：获取任务列表（支持条件筛选）
  - GET `/api/tasks/{id}`：获取任务详情
  - POST `/api/tasks`：创建任务
  - PUT `/api/tasks/{id}`：更新任务信息
  - POST `/api/tasks/{id}/assign`：派单
  - POST `/api/tasks/{id}/accept`：接单
  - POST `/api/tasks/{id}/complete`：完成任务

- **日志管理**
  - GET `/api/task-logs/{taskId}`：查看任务日志

### 6.2 前端主要流程

**H5 用户端**
1. 微信授权 -> 获取 openid -> 后端登录
2. 获取待办任务列表
3. 任务详情 -> 接单/拒单
4. 上传任务执行结果

**PC 管理端**
1. 登录后台
2. 创建任务
3. 选择人员派单
4. 查看任务进度
5. 查看统计数据

---

## 7. 安全与权限
- 基于若依角色控制（ADMIN、EXECUTOR）
- 微信端用户通过 openid 绑定身份
- 所有接口进行权限校验

---

## 8. 部署方案
- 后端 Spring Boot 独立部署
- 前端 Nginx 部署
- Redis 与 MySQL 单节点部署

