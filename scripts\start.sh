#!/bin/bash

# 派单接单系统启动脚本
# 作者: ruoyi
# 版本: 1.0.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 检查Docker和Docker Compose
check_requirements() {
    log_info "检查系统要求..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    log_info "系统要求检查通过"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    mkdir -p nginx/ssl
    mkdir -p logs
    mkdir -p data/mysql
    mkdir -p data/redis
    mkdir -p upload
    
    log_info "目录创建完成"
}

# 构建镜像
build_images() {
    log_info "构建Docker镜像..."
    
    # 构建后端镜像
    log_debug "构建后端镜像..."
    cd backend
    if [ ! -f "target/task-dispatch-system-1.0.0.jar" ]; then
        log_warn "后端jar包不存在，开始构建..."
        if command -v mvn &> /dev/null; then
            mvn clean package -DskipTests
        else
            log_error "Maven 未安装，请先构建后端项目"
            exit 1
        fi
    fi
    cd ..
    
    # 构建前端镜像
    log_debug "构建H5前端镜像..."
    cd frontend/h5
    if [ ! -d "dist" ]; then
        log_warn "H5前端构建产物不存在，开始构建..."
        if command -v npm &> /dev/null; then
            npm install
            npm run build
        else
            log_error "Node.js/npm 未安装，请先构建前端项目"
            exit 1
        fi
    fi
    cd ../..
    
    log_debug "构建PC管理端镜像..."
    cd frontend/admin
    if [ ! -d "dist" ]; then
        log_warn "PC管理端构建产物不存在，开始构建..."
        if command -v npm &> /dev/null; then
            npm install
            npm run build
        else
            log_error "Node.js/npm 未安装，请先构建前端项目"
            exit 1
        fi
    fi
    cd ../..
    
    log_info "镜像构建完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    # 停止已存在的容器
    docker-compose down
    
    # 启动服务
    docker-compose up -d
    
    log_info "服务启动完成"
}

# 检查服务状态
check_services() {
    log_info "检查服务状态..."
    
    sleep 10
    
    # 检查容器状态
    containers=("task-dispatch-mysql" "task-dispatch-redis" "task-dispatch-backend" "task-dispatch-h5" "task-dispatch-admin" "task-dispatch-nginx")
    
    for container in "${containers[@]}"; do
        if docker ps | grep -q "$container"; then
            log_info "✓ $container 运行正常"
        else
            log_error "✗ $container 启动失败"
            docker logs "$container" --tail 20
        fi
    done
    
    # 检查服务可用性
    log_info "检查服务可用性..."
    
    # 检查后端API
    if curl -f http://localhost:8080/api/actuator/health &> /dev/null; then
        log_info "✓ 后端API服务正常"
    else
        log_warn "✗ 后端API服务异常"
    fi
    
    # 检查前端服务
    if curl -f http://localhost:3000 &> /dev/null; then
        log_info "✓ H5前端服务正常"
    else
        log_warn "✗ H5前端服务异常"
    fi
    
    if curl -f http://localhost:3001 &> /dev/null; then
        log_info "✓ PC管理端服务正常"
    else
        log_warn "✗ PC管理端服务异常"
    fi
    
    if curl -f http://localhost/health &> /dev/null; then
        log_info "✓ Nginx代理服务正常"
    else
        log_warn "✗ Nginx代理服务异常"
    fi
}

# 显示访问信息
show_access_info() {
    log_info "系统启动完成！"
    echo ""
    echo "访问地址："
    echo "  H5端（移动端）: http://localhost/h5/"
    echo "  PC管理端:      http://localhost/admin/"
    echo "  后端API:       http://localhost/api/"
    echo ""
    echo "默认账号："
    echo "  管理员: admin / 123456"
    echo "  执行人员: executor1 / 123456"
    echo ""
    echo "数据库连接："
    echo "  地址: localhost:3306"
    echo "  数据库: task_dispatch_system"
    echo "  用户名: task_user"
    echo "  密码: task_password"
    echo ""
    echo "Redis连接："
    echo "  地址: localhost:6379"
    echo ""
    log_info "查看日志: docker-compose logs -f [服务名]"
    log_info "停止服务: docker-compose down"
}

# 主函数
main() {
    log_info "开始启动派单接单系统..."
    
    check_requirements
    create_directories
    build_images
    start_services
    check_services
    show_access_info
    
    log_info "启动完成！"
}

# 执行主函数
main "$@"
