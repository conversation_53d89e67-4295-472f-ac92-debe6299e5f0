import request from '@/utils/request'

// 获取任务列表
export function getTaskList(params) {
  return request({
    url: '/system/task/list',
    method: 'get',
    params
  })
}

// 获取任务详情
export function getTaskDetail(id) {
  return request({
    url: `/system/task/${id}`,
    method: 'get'
  })
}

// 创建任务
export function createTask(data) {
  return request({
    url: '/system/task',
    method: 'post',
    data
  })
}

// 更新任务
export function updateTask(data) {
  return request({
    url: '/system/task',
    method: 'put',
    data
  })
}

// 删除任务
export function deleteTask(ids) {
  return request({
    url: `/system/task/${ids}`,
    method: 'delete'
  })
}

// 派单
export function assignTask(taskId, executorId, operatorId) {
  return request({
    url: `/system/task/${taskId}/assign`,
    method: 'post',
    params: { executorId, operatorId }
  })
}

// 接单
export function acceptTask(taskId, userId) {
  return request({
    url: `/system/task/${taskId}/accept`,
    method: 'post',
    params: { userId }
  })
}

// 拒单
export function rejectTask(taskId, userId, reason) {
  return request({
    url: `/system/task/${taskId}/reject`,
    method: 'post',
    params: { userId, reason }
  })
}

// 开始执行任务
export function startTask(taskId, userId) {
  return request({
    url: `/system/task/${taskId}/start`,
    method: 'post',
    params: { userId }
  })
}

// 完成任务
export function completeTask(taskId, userId, result, images) {
  return request({
    url: `/system/task/${taskId}/complete`,
    method: 'post',
    params: { userId, result, images }
  })
}

// 取消任务
export function cancelTask(taskId, userId, reason) {
  return request({
    url: `/system/task/${taskId}/cancel`,
    method: 'post',
    params: { userId, reason }
  })
}

// 获取用户任务列表
export function getUserTasks(userId, status) {
  return request({
    url: `/system/task/user/${userId}`,
    method: 'get',
    params: { status }
  })
}

// 获取任务统计
export function getTaskStatistics() {
  return request({
    url: '/system/task/statistics',
    method: 'get'
  })
}

// 获取用户任务统计
export function getUserTaskStatistics(userId) {
  return request({
    url: `/system/task/statistics/user/${userId}`,
    method: 'get'
  })
}

// 获取即将到期的任务
export function getTasksNearDeadline(hours = 24) {
  return request({
    url: '/system/task/deadline',
    method: 'get',
    params: { hours }
  })
}
