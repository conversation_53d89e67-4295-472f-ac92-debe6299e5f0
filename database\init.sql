-- 派单接单系统数据库初始化脚本
-- 基于MySQL 8.0+

-- 创建数据库
CREATE DATABASE IF NOT EXISTS task_dispatch_system DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE task_dispatch_system;

-- 用户表
CREATE TABLE `user` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
    `openid` varchar(128) DEFAULT NULL COMMENT '微信openid',
    `username` varchar(50) NOT NULL COMMENT '用户名',
    `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
    `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
    `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
    `role` enum('ADMIN','EXECUTOR') NOT NULL DEFAULT 'EXECUTOR' COMMENT '角色：管理员/执行人员',
    `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_openid` (`openid`),
    UNIQUE KEY `uk_username` (`username`),
    KEY `idx_role` (`role`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 任务表
CREATE TABLE `task` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '任务ID',
    `title` varchar(200) NOT NULL COMMENT '任务标题',
    `description` text COMMENT '任务描述',
    `type` enum('ACTIVATION','INSPECTION','MAINTENANCE','POLICY','FOLLOW_UP') NOT NULL COMMENT '任务类型：激活/巡检/设备维护/政策宣贯/达标回访',
    `status` enum('PENDING','ASSIGNED','IN_PROGRESS','COMPLETED','CANCELLED') NOT NULL DEFAULT 'PENDING' COMMENT '任务状态',
    `priority` tinyint NOT NULL DEFAULT '1' COMMENT '优先级：1-低，2-中，3-高',
    `creator_id` bigint NOT NULL COMMENT '创建人ID',
    `executor_id` bigint DEFAULT NULL COMMENT '执行人ID',
    `merchant_name` varchar(100) DEFAULT NULL COMMENT '商户名称',
    `merchant_address` varchar(255) DEFAULT NULL COMMENT '商户地址',
    `contact_person` varchar(50) DEFAULT NULL COMMENT '联系人',
    `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
    `deadline` datetime DEFAULT NULL COMMENT '截止时间',
    `assigned_at` datetime DEFAULT NULL COMMENT '派单时间',
    `started_at` datetime DEFAULT NULL COMMENT '开始执行时间',
    `completed_at` datetime DEFAULT NULL COMMENT '完成时间',
    `result` text COMMENT '执行结果',
    `images` json COMMENT '上传的图片URLs',
    `remark` text COMMENT '备注',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_creator_id` (`creator_id`),
    KEY `idx_executor_id` (`executor_id`),
    KEY `idx_status` (`status`),
    KEY `idx_type` (`type`),
    KEY `idx_created_at` (`created_at`),
    KEY `idx_deadline` (`deadline`),
    CONSTRAINT `fk_task_creator` FOREIGN KEY (`creator_id`) REFERENCES `user` (`id`),
    CONSTRAINT `fk_task_executor` FOREIGN KEY (`executor_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务表';

-- 任务日志表
CREATE TABLE `task_log` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志ID',
    `task_id` bigint NOT NULL COMMENT '任务ID',
    `action` enum('CREATE','ASSIGN','ACCEPT','REJECT','START','COMPLETE','CANCEL') NOT NULL COMMENT '操作类型',
    `operator_id` bigint NOT NULL COMMENT '操作人ID',
    `operator_name` varchar(50) NOT NULL COMMENT '操作人姓名',
    `remark` text COMMENT '备注',
    `extra_data` json COMMENT '额外数据',
    `ts` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    PRIMARY KEY (`id`),
    KEY `idx_task_id` (`task_id`),
    KEY `idx_operator_id` (`operator_id`),
    KEY `idx_action` (`action`),
    KEY `idx_ts` (`ts`),
    CONSTRAINT `fk_task_log_task` FOREIGN KEY (`task_id`) REFERENCES `task` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_task_log_operator` FOREIGN KEY (`operator_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务日志表';

-- 文件上传表
CREATE TABLE `file_upload` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '文件ID',
    `original_name` varchar(255) NOT NULL COMMENT '原始文件名',
    `file_name` varchar(255) NOT NULL COMMENT '存储文件名',
    `file_path` varchar(500) NOT NULL COMMENT '文件路径',
    `file_size` bigint NOT NULL COMMENT '文件大小(字节)',
    `file_type` varchar(50) NOT NULL COMMENT '文件类型',
    `uploader_id` bigint NOT NULL COMMENT '上传人ID',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
    PRIMARY KEY (`id`),
    KEY `idx_uploader_id` (`uploader_id`),
    KEY `idx_created_at` (`created_at`),
    CONSTRAINT `fk_file_uploader` FOREIGN KEY (`uploader_id`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件上传表';

-- 插入初始数据
-- 管理员用户
INSERT INTO `user` (`username`, `nickname`, `role`, `status`) VALUES 
('admin', '系统管理员', 'ADMIN', 1),
('manager1', '运营经理', 'ADMIN', 1);

-- 执行人员
INSERT INTO `user` (`username`, `nickname`, `phone`, `role`, `status`) VALUES 
('executor1', '张三', '13800138001', 'EXECUTOR', 1),
('executor2', '李四', '13800138002', 'EXECUTOR', 1),
('executor3', '王五', '13800138003', 'EXECUTOR', 1);

-- 示例任务
INSERT INTO `task` (`title`, `description`, `type`, `creator_id`, `merchant_name`, `merchant_address`, `contact_person`, `contact_phone`, `deadline`) VALUES 
('商户激活任务', '协助新商户完成系统激活和基础培训', 'ACTIVATION', 1, '张记小吃店', '北京市朝阳区建国路88号', '张老板', '13900139001', DATE_ADD(NOW(), INTERVAL 3 DAY)),
('设备巡检任务', '对商户POS设备进行例行巡检', 'INSPECTION', 1, '李记餐厅', '北京市海淀区中关村大街100号', '李经理', '13900139002', DATE_ADD(NOW(), INTERVAL 2 DAY)),
('政策宣贯任务', '向商户宣传最新的优惠政策', 'POLICY', 2, '王氏超市', '北京市西城区西单大街50号', '王总', '13900139003', DATE_ADD(NOW(), INTERVAL 5 DAY));

-- 创建视图：任务统计
CREATE VIEW `task_statistics` AS
SELECT 
    DATE(created_at) as date,
    type,
    status,
    COUNT(*) as count
FROM task 
GROUP BY DATE(created_at), type, status;

-- 创建视图：用户任务统计
CREATE VIEW `user_task_statistics` AS
SELECT 
    u.id as user_id,
    u.username,
    u.nickname,
    COUNT(t.id) as total_tasks,
    COUNT(CASE WHEN t.status = 'COMPLETED' THEN 1 END) as completed_tasks,
    COUNT(CASE WHEN t.status = 'IN_PROGRESS' THEN 1 END) as in_progress_tasks
FROM user u
LEFT JOIN task t ON u.id = t.executor_id
WHERE u.role = 'EXECUTOR'
GROUP BY u.id, u.username, u.nickname;
