package com.ruoyi.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.enums.TaskLogAction;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 任务日志对象 task_log
 * 
 * <AUTHOR>
 */
@TableName("task_log")
public class TaskLog implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /** 日志ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 任务ID */
    private Long taskId;

    /** 操作类型 */
    private TaskLogAction action;

    /** 操作人ID */
    private Long operatorId;

    /** 操作人姓名 */
    private String operatorName;

    /** 备注 */
    private String remark;

    /** 额外数据 */
    private String extraData;

    /** 操作时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime ts;

    public TaskLog() {
    }

    public TaskLog(Long taskId, TaskLogAction action, Long operatorId, String operatorName) {
        this.taskId = taskId;
        this.action = action;
        this.operatorId = operatorId;
        this.operatorName = operatorName;
    }

    public TaskLog(Long taskId, TaskLogAction action, Long operatorId, String operatorName, String remark) {
        this.taskId = taskId;
        this.action = action;
        this.operatorId = operatorId;
        this.operatorName = operatorName;
        this.remark = remark;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public TaskLogAction getAction() {
        return action;
    }

    public void setAction(TaskLogAction action) {
        this.action = action;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getExtraData() {
        return extraData;
    }

    public void setExtraData(String extraData) {
        this.extraData = extraData;
    }

    public LocalDateTime getTs() {
        return ts;
    }

    public void setTs(LocalDateTime ts) {
        this.ts = ts;
    }

    @Override
    public String toString() {
        return "TaskLog{" +
                "id=" + id +
                ", taskId=" + taskId +
                ", action=" + action +
                ", operatorId=" + operatorId +
                ", operatorName='" + operatorName + '\'' +
                ", remark='" + remark + '\'' +
                ", extraData='" + extraData + '\'' +
                ", ts=" + ts +
                '}';
    }
}
