package com.ruoyi.system.service;

import com.ruoyi.system.domain.SysUser;

import java.util.List;

/**
 * 用户 业务层
 * 
 * <AUTHOR>
 */
public interface ISysUserService {
    
    /**
     * 根据条件分页查询用户列表
     * 
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    List<SysUser> selectUserList(SysUser user);

    /**
     * 根据用户ID查询用户
     * 
     * @param userId 用户ID
     * @return 用户对象信息
     */
    SysUser selectUserById(Long userId);

    /**
     * 根据用户名查询用户
     * 
     * @param username 用户名
     * @return 用户对象信息
     */
    SysUser selectUserByUsername(String username);

    /**
     * 根据openid查询用户
     * 
     * @param openid 微信openid
     * @return 用户对象信息
     */
    SysUser selectUserByOpenid(String openid);

    /**
     * 校验用户名称是否唯一
     * 
     * @param user 用户信息
     * @return 结果
     */
    String checkUsernameUnique(SysUser user);

    /**
     * 校验openid是否唯一
     * 
     * @param user 用户信息
     * @return 结果
     */
    String checkOpenidUnique(SysUser user);

    /**
     * 新增用户
     * 
     * @param user 用户信息
     * @return 结果
     */
    int insertUser(SysUser user);

    /**
     * 修改用户
     * 
     * @param user 用户信息
     * @return 结果
     */
    int updateUser(SysUser user);

    /**
     * 删除用户
     * 
     * @param userId 用户ID
     * @return 结果
     */
    int deleteUserById(Long userId);

    /**
     * 批量删除用户
     * 
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
    int deleteUserByIds(Long[] userIds);

    /**
     * 查询执行人员列表
     * 
     * @return 执行人员列表
     */
    List<SysUser> selectExecutorList();

    /**
     * 微信登录或注册
     * 
     * @param openid 微信openid
     * @param nickname 昵称
     * @param avatar 头像
     * @return 用户信息
     */
    SysUser wechatLoginOrRegister(String openid, String nickname, String avatar);
}
