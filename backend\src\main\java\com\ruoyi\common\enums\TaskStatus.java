package com.ruoyi.common.enums;

/**
 * 任务状态枚举
 * 
 * <AUTHOR>
 */
public enum TaskStatus {
    
    PENDING("PENDING", "待派单"),
    ASSIGNED("ASSIGNED", "已派单"),
    IN_PROGRESS("IN_PROGRESS", "执行中"),
    COMPLETED("COMPLETED", "已完成"),
    CANCELLED("CANCELLED", "已取消");
    
    private final String code;
    private final String info;
    
    TaskStatus(String code, String info) {
        this.code = code;
        this.info = info;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getInfo() {
        return info;
    }
}
