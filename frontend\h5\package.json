{"name": "task-dispatch-h5", "version": "1.0.0", "description": "派单接单系统H5端", "main": "index.js", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"vue": "^3.3.4", "vue-router": "^4.2.4", "pinia": "^2.1.6", "axios": "^1.4.0", "vant": "^4.6.2", "@vant/touch-emulator": "^1.4.0", "dayjs": "^1.11.9", "weixin-js-sdk": "^1.6.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.2.3", "vite": "^4.4.5", "vite-plugin-windicss": "^1.9.1", "windicss": "^3.5.6", "@types/weixin-js-sdk": "^1.6.0"}, "keywords": ["vue3", "vite", "h5", "mobile", "task-dispatch"], "author": "ruoyi", "license": "MIT"}