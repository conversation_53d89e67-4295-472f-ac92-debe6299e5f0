package com.ruoyi.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.system.domain.TaskLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 任务日志Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface TaskLogMapper extends BaseMapper<TaskLog> {
    
    /**
     * 根据任务ID查询日志列表
     * 
     * @param taskId 任务ID
     * @return 日志列表
     */
    List<TaskLog> selectLogsByTaskId(@Param("taskId") Long taskId);
    
    /**
     * 根据操作人ID查询日志列表
     * 
     * @param operatorId 操作人ID
     * @return 日志列表
     */
    List<TaskLog> selectLogsByOperatorId(@Param("operatorId") Long operatorId);
    
    /**
     * 批量插入日志
     * 
     * @param logs 日志列表
     * @return 影响行数
     */
    int insertBatch(@Param("logs") List<TaskLog> logs);
}
