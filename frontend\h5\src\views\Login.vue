<template>
  <div class="login-container">
    <div class="login-header">
      <div class="logo">
        <van-icon name="orders-o" size="60" color="#1989fa" />
      </div>
      <h1 class="title">派单接单系统</h1>
      <p class="subtitle">高效任务管理，轻松派单接单</p>
    </div>

    <div class="login-form">
      <van-form @submit="handleLogin">
        <van-field
          v-model="form.openid"
          name="openid"
          label="OpenID"
          placeholder="请输入微信OpenID"
          :rules="[{ required: true, message: '请输入微信OpenID' }]"
        />
        <van-field
          v-model="form.nickname"
          name="nickname"
          label="昵称"
          placeholder="请输入昵称（可选）"
        />
        
        <div class="login-btn">
          <van-button
            round
            block
            type="primary"
            native-type="submit"
            :loading="loading"
            loading-text="登录中..."
          >
            登录
          </van-button>
        </div>
      </van-form>

      <div class="wechat-login">
        <van-divider>或</van-divider>
        <van-button
          round
          block
          type="success"
          icon="wechat"
          @click="handleWechatLogin"
          :loading="wechatLoading"
        >
          微信授权登录
        </van-button>
      </div>
    </div>

    <div class="login-footer">
      <p class="tips">
        首次登录将自动注册账号<br>
        登录即表示同意用户协议和隐私政策
      </p>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import { useUserStore } from '@/stores/user'
import { getAuthUrl } from '@/api/auth'

const router = useRouter()
const userStore = useUserStore()

const loading = ref(false)
const wechatLoading = ref(false)

const form = reactive({
  openid: '',
  nickname: ''
})

// 普通登录
const handleLogin = async () => {
  loading.value = true
  try {
    await userStore.loginUser(form)
    showToast({
      message: '登录成功',
      type: 'success'
    })
    router.push('/tasks')
  } catch (error) {
    console.error('登录失败:', error)
  } finally {
    loading.value = false
  }
}

// 微信授权登录
const handleWechatLogin = async () => {
  wechatLoading.value = true
  try {
    // 获取当前页面URL作为回调地址
    const redirectUri = encodeURIComponent(window.location.origin + '/login')
    const res = await getAuthUrl(redirectUri)
    
    // 跳转到微信授权页面
    window.location.href = res.data
  } catch (error) {
    console.error('获取微信授权URL失败:', error)
    showToast({
      message: '微信授权失败',
      type: 'fail'
    })
  } finally {
    wechatLoading.value = false
  }
}

// 检查URL参数中是否有授权码
const checkAuthCode = () => {
  const urlParams = new URLSearchParams(window.location.search)
  const code = urlParams.get('code')
  
  if (code) {
    // 处理微信授权回调
    handleWechatCallback(code)
  }
}

// 处理微信授权回调
const handleWechatCallback = async (code) => {
  loading.value = true
  try {
    const res = await authCallback(code)
    userStore.token = res.data.token
    userStore.userInfo = res.data.user
    localStorage.setItem('token', res.data.token)
    
    showToast({
      message: '登录成功',
      type: 'success'
    })
    
    // 清除URL参数
    window.history.replaceState({}, document.title, window.location.pathname)
    
    router.push('/tasks')
  } catch (error) {
    console.error('微信授权回调失败:', error)
  } finally {
    loading.value = false
  }
}

// 页面加载时检查授权码
checkAuthCode()
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  padding: 20px;
}

.login-header {
  text-align: center;
  margin-top: 60px;
  margin-bottom: 40px;
}

.logo {
  margin-bottom: 20px;
}

.title {
  color: white;
  font-size: 28px;
  font-weight: bold;
  margin: 0 0 10px 0;
}

.subtitle {
  color: rgba(255, 255, 255, 0.8);
  font-size: 16px;
  margin: 0;
}

.login-form {
  background: white;
  border-radius: 16px;
  padding: 30px 20px;
  margin-bottom: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.login-btn {
  margin-top: 30px;
}

.wechat-login {
  margin-top: 20px;
}

.login-footer {
  text-align: center;
  margin-top: auto;
}

.tips {
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  line-height: 1.5;
  margin: 0;
}
</style>
