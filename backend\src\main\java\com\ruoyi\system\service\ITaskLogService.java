package com.ruoyi.system.service;

import com.ruoyi.system.domain.TaskLog;

import java.util.List;

/**
 * 任务日志 业务层
 * 
 * <AUTHOR>
 */
public interface ITaskLogService {
    
    /**
     * 查询任务日志列表
     * 
     * @param taskLog 任务日志
     * @return 任务日志集合
     */
    List<TaskLog> selectTaskLogList(TaskLog taskLog);

    /**
     * 根据任务ID查询日志列表
     * 
     * @param taskId 任务ID
     * @return 日志列表
     */
    List<TaskLog> selectLogsByTaskId(Long taskId);

    /**
     * 根据操作人ID查询日志列表
     * 
     * @param operatorId 操作人ID
     * @return 日志列表
     */
    List<TaskLog> selectLogsByOperatorId(Long operatorId);

    /**
     * 新增任务日志
     * 
     * @param taskLog 任务日志
     * @return 结果
     */
    int insertTaskLog(TaskLog taskLog);

    /**
     * 批量新增任务日志
     * 
     * @param logs 日志列表
     * @return 结果
     */
    int insertBatch(List<TaskLog> logs);

    /**
     * 删除任务日志
     * 
     * @param id 任务日志主键
     * @return 结果
     */
    int deleteTaskLogById(Long id);

    /**
     * 批量删除任务日志
     * 
     * @param ids 需要删除的任务日志主键集合
     * @return 结果
     */
    int deleteTaskLogByIds(Long[] ids);
}
