<component name="libraryTable">
  <library name="Maven: org.springframework.data:spring-data-redis:2.7.18">
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/org/springframework/data/spring-data-redis/2.7.18/spring-data-redis-2.7.18.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/org/springframework/data/spring-data-redis/2.7.18/spring-data-redis-2.7.18-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/org/springframework/data/spring-data-redis/2.7.18/spring-data-redis-2.7.18-sources.jar!/" />
    </SOURCES>
  </library>
</component>