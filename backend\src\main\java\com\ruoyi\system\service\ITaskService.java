package com.ruoyi.system.service;

import com.ruoyi.system.domain.Task;

import java.util.List;
import java.util.Map;

/**
 * 任务 业务层
 * 
 * <AUTHOR>
 */
public interface ITaskService {
    
    /**
     * 查询任务列表
     * 
     * @param task 任务信息
     * @return 任务集合
     */
    List<Task> selectTaskList(Task task);

    /**
     * 根据任务ID查询任务详情
     * 
     * @param taskId 任务ID
     * @return 任务对象
     */
    Task selectTaskById(Long taskId);

    /**
     * 新增任务
     * 
     * @param task 任务信息
     * @return 结果
     */
    int insertTask(Task task);

    /**
     * 修改任务
     * 
     * @param task 任务信息
     * @return 结果
     */
    int updateTask(Task task);

    /**
     * 删除任务
     * 
     * @param taskId 任务ID
     * @return 结果
     */
    int deleteTaskById(Long taskId);

    /**
     * 批量删除任务
     * 
     * @param taskIds 需要删除的任务ID
     * @return 结果
     */
    int deleteTaskByIds(Long[] taskIds);

    /**
     * 派单
     * 
     * @param taskId 任务ID
     * @param executorId 执行人ID
     * @param operatorId 操作人ID
     * @return 结果
     */
    boolean assignTask(Long taskId, Long executorId, Long operatorId);

    /**
     * 接单
     * 
     * @param taskId 任务ID
     * @param userId 用户ID
     * @return 结果
     */
    boolean acceptTask(Long taskId, Long userId);

    /**
     * 拒单
     * 
     * @param taskId 任务ID
     * @param userId 用户ID
     * @param reason 拒绝原因
     * @return 结果
     */
    boolean rejectTask(Long taskId, Long userId, String reason);

    /**
     * 开始执行任务
     * 
     * @param taskId 任务ID
     * @param userId 用户ID
     * @return 结果
     */
    boolean startTask(Long taskId, Long userId);

    /**
     * 完成任务
     * 
     * @param taskId 任务ID
     * @param userId 用户ID
     * @param result 执行结果
     * @param images 图片URLs
     * @return 结果
     */
    boolean completeTask(Long taskId, Long userId, String result, String images);

    /**
     * 取消任务
     * 
     * @param taskId 任务ID
     * @param userId 用户ID
     * @param reason 取消原因
     * @return 结果
     */
    boolean cancelTask(Long taskId, Long userId, String reason);

    /**
     * 查询用户的任务列表
     * 
     * @param userId 用户ID
     * @param status 任务状态（可选）
     * @return 任务列表
     */
    List<Task> selectTasksByUserId(Long userId, String status);

    /**
     * 查询任务统计数据
     * 
     * @return 统计数据
     */
    List<Map<String, Object>> selectTaskStatistics();

    /**
     * 查询用户任务统计数据
     * 
     * @param userId 用户ID
     * @return 统计数据
     */
    Map<String, Object> selectUserTaskStatistics(Long userId);

    /**
     * 查询即将到期的任务
     * 
     * @param hours 小时数
     * @return 任务列表
     */
    List<Task> selectTasksNearDeadline(int hours);
}
