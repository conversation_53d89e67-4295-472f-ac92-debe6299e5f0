package com.ruoyi.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.system.domain.SysUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户信息Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface SysUserMapper extends BaseMapper<SysUser> {
    
    /**
     * 根据openid查询用户
     * 
     * @param openid 微信openid
     * @return 用户信息
     */
    SysUser selectUserByOpenid(@Param("openid") String openid);
    
    /**
     * 根据用户名查询用户
     * 
     * @param username 用户名
     * @return 用户信息
     */
    SysUser selectUserByUsername(@Param("username") String username);
    
    /**
     * 查询执行人员列表
     * 
     * @return 执行人员列表
     */
    List<SysUser> selectExecutorList();
    
    /**
     * 检查用户名是否唯一
     * 
     * @param username 用户名
     * @param userId 用户ID（排除自己）
     * @return 结果
     */
    int checkUsernameUnique(@Param("username") String username, @Param("userId") Long userId);
    
    /**
     * 检查openid是否唯一
     * 
     * @param openid 微信openid
     * @param userId 用户ID（排除自己）
     * @return 结果
     */
    int checkOpenidUnique(@Param("openid") String openid, @Param("userId") Long userId);
}
