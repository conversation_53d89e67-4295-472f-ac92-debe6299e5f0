package com.ruoyi.common.enums;

/**
 * 任务类型枚举
 * 
 * <AUTHOR>
 */
public enum TaskType {
    
    ACTIVATION("ACTIVATION", "激活"),
    INSPECTION("INSPECTION", "巡检"),
    MAINTENANCE("MAINTE<PERSON><PERSON><PERSON>", "设备维护"),
    POLICY("POLICY", "政策宣贯"),
    FOLLOW_UP("FOLLOW_UP", "达标回访");
    
    private final String code;
    private final String info;
    
    TaskType(String code, String info) {
        this.code = code;
        this.info = info;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getInfo() {
        return info;
    }
}
