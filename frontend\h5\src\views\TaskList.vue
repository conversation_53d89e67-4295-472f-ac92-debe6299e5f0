<template>
  <div class="task-list-container">
    <!-- 头部 -->
    <van-nav-bar
      title="任务列表"
      left-text="返回"
      right-text="个人中心"
      left-arrow
      @click-left="$router.go(-1)"
      @click-right="$router.push('/profile')"
    />

    <!-- 筛选栏 -->
    <div class="filter-bar">
      <van-dropdown-menu>
        <van-dropdown-item
          v-model="filterStatus"
          :options="statusOptions"
          @change="handleFilterChange"
        />
        <van-dropdown-item
          v-model="filterType"
          :options="typeOptions"
          @change="handleFilterChange"
        />
      </van-dropdown-menu>
    </div>

    <!-- 任务列表 -->
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <van-list
        v-model:loading="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoad"
      >
        <div v-for="task in taskList" :key="task.id" class="task-item">
          <van-card
            :title="task.title"
            :desc="task.description"
            :thumb="getTaskTypeIcon(task.type)"
            @click="goToDetail(task.id)"
          >
            <template #tags>
              <van-tag
                :type="getStatusTagType(task.status)"
                size="medium"
                class="status-tag"
              >
                {{ getStatusText(task.status) }}
              </van-tag>
              <van-tag
                type="primary"
                size="medium"
                plain
                class="type-tag"
              >
                {{ getTypeText(task.type) }}
              </van-tag>
            </template>
            
            <template #bottom>
              <div class="task-info">
                <div class="info-row">
                  <van-icon name="user-o" />
                  <span>创建人：{{ task.creatorName }}</span>
                </div>
                <div class="info-row" v-if="task.executorName">
                  <van-icon name="manager-o" />
                  <span>执行人：{{ task.executorName }}</span>
                </div>
                <div class="info-row" v-if="task.merchantName">
                  <van-icon name="shop-o" />
                  <span>商户：{{ task.merchantName }}</span>
                </div>
                <div class="info-row" v-if="task.deadline">
                  <van-icon name="clock-o" />
                  <span>截止：{{ formatDate(task.deadline) }}</span>
                </div>
              </div>
              
              <!-- 操作按钮 -->
              <div class="action-buttons" v-if="showActionButtons(task)">
                <van-button
                  v-if="task.status === 'ASSIGNED' && task.executorId === userStore.userInfo?.id"
                  type="success"
                  size="small"
                  @click.stop="handleAccept(task.id)"
                >
                  接单
                </van-button>
                <van-button
                  v-if="task.status === 'ASSIGNED' && task.executorId === userStore.userInfo?.id"
                  type="warning"
                  size="small"
                  @click.stop="handleReject(task.id)"
                >
                  拒单
                </van-button>
                <van-button
                  v-if="task.status === 'IN_PROGRESS' && task.executorId === userStore.userInfo?.id"
                  type="primary"
                  size="small"
                  @click.stop="goToExecute(task.id)"
                >
                  执行
                </van-button>
              </div>
            </template>
          </van-card>
        </div>
      </van-list>
    </van-pull-refresh>

    <!-- 浮动按钮 -->
    <van-floating-bubble
      v-if="userStore.isAdmin"
      axis="xy"
      icon="plus"
      @click="$router.push('/create-task')"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showDialog, showToast } from 'vant'
import { useUserStore } from '@/stores/user'
import { getTaskList, acceptTask, rejectTask } from '@/api/task'
import { formatDate } from '@/utils/date'

const router = useRouter()
const userStore = useUserStore()

const refreshing = ref(false)
const loading = ref(false)
const finished = ref(false)
const taskList = ref([])

const filterStatus = ref('')
const filterType = ref('')

const statusOptions = [
  { text: '全部状态', value: '' },
  { text: '待派单', value: 'PENDING' },
  { text: '已派单', value: 'ASSIGNED' },
  { text: '执行中', value: 'IN_PROGRESS' },
  { text: '已完成', value: 'COMPLETED' },
  { text: '已取消', value: 'CANCELLED' }
]

const typeOptions = [
  { text: '全部类型', value: '' },
  { text: '激活', value: 'ACTIVATION' },
  { text: '巡检', value: 'INSPECTION' },
  { text: '设备维护', value: 'MAINTENANCE' },
  { text: '政策宣贯', value: 'POLICY' },
  { text: '达标回访', value: 'FOLLOW_UP' }
]

// 获取任务列表
const fetchTaskList = async (isRefresh = false) => {
  try {
    const params = {
      status: filterStatus.value,
      type: filterType.value
    }
    
    const res = await getTaskList(params)
    
    if (isRefresh) {
      taskList.value = res.data
    } else {
      taskList.value.push(...res.data)
    }
    
    finished.value = res.data.length < 20
  } catch (error) {
    console.error('获取任务列表失败:', error)
  }
}

// 下拉刷新
const onRefresh = async () => {
  finished.value = false
  await fetchTaskList(true)
  refreshing.value = false
}

// 上拉加载
const onLoad = async () => {
  await fetchTaskList()
  loading.value = false
}

// 筛选变化
const handleFilterChange = () => {
  taskList.value = []
  finished.value = false
  fetchTaskList(true)
}

// 跳转到详情页
const goToDetail = (taskId) => {
  router.push(`/tasks/${taskId}`)
}

// 跳转到执行页
const goToExecute = (taskId) => {
  router.push(`/tasks/${taskId}/execute`)
}

// 接单
const handleAccept = async (taskId) => {
  try {
    await acceptTask(taskId, userStore.userInfo.id)
    showToast({
      message: '接单成功',
      type: 'success'
    })
    onRefresh()
  } catch (error) {
    console.error('接单失败:', error)
  }
}

// 拒单
const handleReject = async (taskId) => {
  showDialog({
    title: '拒绝任务',
    message: '请输入拒绝原因',
    showCancelButton: true,
    showInput: true,
    inputPlaceholder: '请输入拒绝原因'
  }).then(async ({ value }) => {
    try {
      await rejectTask(taskId, userStore.userInfo.id, value)
      showToast({
        message: '拒单成功',
        type: 'success'
      })
      onRefresh()
    } catch (error) {
      console.error('拒单失败:', error)
    }
  })
}

// 获取状态标签类型
const getStatusTagType = (status) => {
  const typeMap = {
    'PENDING': 'default',
    'ASSIGNED': 'warning',
    'IN_PROGRESS': 'primary',
    'COMPLETED': 'success',
    'CANCELLED': 'danger'
  }
  return typeMap[status] || 'default'
}

// 获取状态文本
const getStatusText = (status) => {
  const textMap = {
    'PENDING': '待派单',
    'ASSIGNED': '已派单',
    'IN_PROGRESS': '执行中',
    'COMPLETED': '已完成',
    'CANCELLED': '已取消'
  }
  return textMap[status] || status
}

// 获取类型文本
const getTypeText = (type) => {
  const textMap = {
    'ACTIVATION': '激活',
    'INSPECTION': '巡检',
    'MAINTENANCE': '设备维护',
    'POLICY': '政策宣贯',
    'FOLLOW_UP': '达标回访'
  }
  return textMap[type] || type
}

// 获取任务类型图标
const getTaskTypeIcon = (type) => {
  const iconMap = {
    'ACTIVATION': 'https://img.yzcdn.cn/vant/cat.jpeg',
    'INSPECTION': 'https://img.yzcdn.cn/vant/cat.jpeg',
    'MAINTENANCE': 'https://img.yzcdn.cn/vant/cat.jpeg',
    'POLICY': 'https://img.yzcdn.cn/vant/cat.jpeg',
    'FOLLOW_UP': 'https://img.yzcdn.cn/vant/cat.jpeg'
  }
  return iconMap[type] || 'https://img.yzcdn.cn/vant/cat.jpeg'
}

// 是否显示操作按钮
const showActionButtons = (task) => {
  const userId = userStore.userInfo?.id
  return (task.status === 'ASSIGNED' || task.status === 'IN_PROGRESS') && 
         task.executorId === userId
}

onMounted(() => {
  fetchTaskList(true)
})
</script>

<style scoped>
.task-list-container {
  background-color: #f7f8fa;
  min-height: 100vh;
}

.filter-bar {
  background: white;
  border-bottom: 1px solid #ebedf0;
}

.task-item {
  margin: 12px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.status-tag {
  margin-right: 8px;
}

.type-tag {
  margin-right: 8px;
}

.task-info {
  margin-top: 12px;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  font-size: 12px;
  color: #969799;
}

.info-row .van-icon {
  margin-right: 4px;
}

.action-buttons {
  margin-top: 12px;
  display: flex;
  gap: 8px;
}
</style>
