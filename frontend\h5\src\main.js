import { createApp } from 'vue'
import { createPinia } from 'pinia'
import router from './router'
import App from './App.vue'

// Vant组件库
import Vant from 'vant'
import 'vant/lib/index.css'

// Touch模拟器（开发环境）
import '@vant/touch-emulator'

// WindiCSS
import 'virtual:windi.css'

// 全局样式
import './styles/index.css'

const app = createApp(App)

app.use(createPinia())
app.use(router)
app.use(Vant)

app.mount('#app')
