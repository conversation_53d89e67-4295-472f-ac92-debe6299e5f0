package com.ruoyi.common.enums;

/**
 * 用户角色枚举
 * 
 * <AUTHOR>
 */
public enum UserRole {
    
    ADMIN("ADMIN", "管理员"),
    EXECUTOR("EXECUTOR", "执行人员");
    
    private final String code;
    private final String info;
    
    UserRole(String code, String info) {
        this.code = code;
        this.info = info;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getInfo() {
        return info;
    }
}
